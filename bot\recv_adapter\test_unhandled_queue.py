#!/usr/bin/env python3
"""
测试未处理消息转发到wxid队列的功能
"""
import asyncio
import json
from datetime import datetime
from utils.mq_publisher import UnifiedMQPublisher
from framework.core.models import StandardMessage
from config import settings

async def test_unhandled_message_forwarding():
    """测试未处理消息转发功能"""

    # 创建统一MQ发布器
    publisher = UnifiedMQPublisher()

    try:
        # 连接到RabbitMQ
        await publisher.connect()
        print("Connected to RabbitMQ")

        # 创建测试消息
        test_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=12345,
            msg_type=1,
            timestamp=int(datetime.now().timestamp()),
            is_self_message=False,
            wxid="test_wxid_123",
            uuid="test_uuid",
            from_user_name="test_user",
            to_user_name="test_target",
            content="这是一条测试未处理消息",
            push_content="",
            msg_source="",
            ver=1
        )

        print(f"Test message created: {test_message.msg_id}")

        # 转换为字典
        message_data = test_message.model_dump(exclude_none=True)

        # 发布到wxid队列
        success = await publisher.publish_unhandled_message(
            message_data,
            test_message.wxid
        )

        if success:
            print(f"✅ Successfully published test message to queue unhandled.{test_message.wxid}")
        else:
            print(f"❌ Failed to publish test message")

        # 等待一段时间让消息处理
        await asyncio.sleep(2)

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 断开连接
        await publisher.disconnect()
        print("Disconnected from RabbitMQ")

async def test_queue_consumer():
    """测试消费wxid队列中的消息"""

    publisher = UnifiedMQPublisher()
    
    try:
        await publisher.connect()
        print("Connected to RabbitMQ for consuming")

        # 测试队列名称
        test_wxid = "test_wxid_123"
        queue_name = f"unhandled.{test_wxid}"

        # 声明队列（被动模式，不创建新队列）
        try:
            queue = await publisher.channel.declare_queue(queue_name, passive=True)
            print(f"Queue {queue_name} exists")

            # 获取队列信息
            queue_info = await publisher.channel.queue_declare(queue_name, passive=True)
            message_count = queue_info.method.message_count
            print(f"Queue {queue_name} has {message_count} messages")

            if message_count > 0:
                print("Consuming messages from queue...")

                # 消费消息
                async def message_handler(message):
                    async with message.process():
                        try:
                            body = message.body.decode('utf-8')
                            data = json.loads(body)
                            print(f"📨 Received message: {data.get('msg_id')} - {data.get('content')}")
                        except Exception as e:
                            print(f"Error processing message: {e}")

                # 开始消费（只消费一条消息用于测试）
                consumer_tag = await queue.consume(message_handler)

                # 等待消息处理
                await asyncio.sleep(5)

                # 停止消费
                await publisher.channel.basic_cancel(consumer_tag)
                print("Stopped consuming")
            else:
                print("No messages in queue")

        except Exception as e:
            print(f"Queue {queue_name} does not exist or error: {e}")

    except Exception as e:
        print(f"Consumer test failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await publisher.disconnect()
        print("Consumer disconnected")

async def main():
    """主测试函数"""
    print("🧪 Testing unhandled message forwarding to wxid queues")
    print("=" * 60)
    
    print("\n1. Testing message publishing...")
    await test_unhandled_message_forwarding()
    
    print("\n2. Testing message consuming...")
    await test_queue_consumer()
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
