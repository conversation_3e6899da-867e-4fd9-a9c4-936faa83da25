"""
应用程序配置管理
扩展现有配置以支持数据库和消息队列
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    try:
        from pydantic import BaseSettings, Field
    except ImportError:
        # 简化版配置，不依赖pydantic
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

            def dict(self):
                return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}

        def Field(default=None, env=None, description=None):
            return default


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    # MySQL配置
    db_host: str = Field(default="rm-uf6psdjix97ed81r8oo.mysql.rds.aliyuncs.com", env="DB_HOST")
    db_port: int = Field(default=3306, env="DB_PORT")
    db_user: str = Field(default="bot", env="DB_USER")
    db_password: str = Field(default="yaoboan19990312!", env="DB_PASSWORD")
    db_name: str = Field(default="yba_ppmt", env="DB_NAME")
    db_charset: str = Field(default="utf8mb4", env="DB_CHARSET")
    
    # 连接池配置
    db_pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    db_max_overflow: int = Field(default=20, env="DB_MAX_OVERFLOW")
    db_pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    
    # 是否使用模拟数据
    use_mock_db: bool = Field(default=False, env="USE_MOCK_DB")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class MessageQueueConfig(BaseSettings):
    """消息队列配置"""
    
    # RabbitMQ配置
    mq_host: str = Field(default="**************", env="MQ_HOST")
    mq_port: int = Field(default=5672, env="MQ_PORT")
    mq_username: str = Field(default="bot", env="MQ_USERNAME")
    mq_password: str = Field(default="bot", env="MQ_PASSWORD")
    mq_virtual_host: str = Field(default="/", env="MQ_VIRTUAL_HOST")
    
    # 交换机和队列配置
    mq_exchange: str = Field(default="wechat_messages", env="MQ_EXCHANGE")
    mq_queue_prefix: str = Field(default="group_", env="MQ_QUEUE_PREFIX")
    mq_routing_key_prefix: str = Field(default="group.", env="MQ_ROUTING_KEY_PREFIX")
    
    # 消费者配置
    mq_prefetch_count: int = Field(default=1, env="MQ_PREFETCH_COUNT")
    mq_heartbeat: int = Field(default=600, env="MQ_HEARTBEAT")
    mq_blocked_connection_timeout: int = Field(default=300, env="MQ_BLOCKED_CONNECTION_TIMEOUT")
    
    # 是否使用模拟消息队列
    use_mock_mq: bool = Field(default=False, env="USE_MOCK_MQ")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class AppConfig(BaseSettings):
    """应用程序配置"""
    
    # 应用基础配置
    app_name: str = Field(default="WechatAPI", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    app_debug: bool = Field(default=False, env="APP_DEBUG")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    # 健康检查配置
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    stats_output_interval: int = Field(default=300, env="STATS_OUTPUT_INTERVAL")
    
    # 账号刷新配置
    account_refresh_interval: int = Field(default=300, env="ACCOUNT_REFRESH_INTERVAL")
    
    # 故障转移配置
    failover_min_switch_interval: int = Field(default=60, env="FAILOVER_MIN_SWITCH_INTERVAL")
    failover_max_error_count: int = Field(default=5, env="FAILOVER_MAX_ERROR_COUNT")
    
    # 并发配置
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class Config:
    """统一配置类"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.message_queue = MessageQueueConfig()
        self.app = AppConfig()
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return (f"mysql+pymysql://{self.database.db_user}:{self.database.db_password}"
                f"@{self.database.db_host}:{self.database.db_port}/{self.database.db_name}"
                f"?charset={self.database.db_charset}")
    
    def get_rabbitmq_url(self) -> str:
        """获取RabbitMQ连接URL"""
        return (f"amqp://{self.message_queue.mq_username}:{self.message_queue.mq_password}"
                f"@{self.message_queue.mq_host}:{self.message_queue.mq_port}"
                f"{self.message_queue.mq_virtual_host}")
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.app.app_debug
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return not self.app.app_debug
    
    def should_use_mock_db(self) -> bool:
        """是否应该使用模拟数据库"""
        return self.database.use_mock_db
    
    def should_use_mock_mq(self) -> bool:
        """是否应该使用模拟消息队列"""
        return self.message_queue.use_mock_mq
    
    def get_log_config(self) -> dict:
        """获取日志配置"""
        config = {
            "level": self.app.log_level,
            "format": ("<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                      "<level>{level: <8}</level> | "
                      "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                      "<level>{message}</level>")
        }
        
        if self.app.log_file:
            config.update({
                "rotation": self.app.log_rotation,
                "retention": self.app.log_retention,
                "compression": "zip"
            })
        
        return config
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "database": self.database.dict(),
            "message_queue": self.message_queue.dict(),
            "app": self.app.dict()
        }


# 全局配置实例
config = Config()


def load_config_from_file(config_file: str = ".env"):
    """从文件加载配置"""
    if os.path.exists(config_file):
        # 重新加载配置
        global config
        config = Config()
        return True
    return False


def get_config() -> Config:
    """获取全局配置"""
    return config


# 环境变量示例
ENV_EXAMPLE = """
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=yba_ppmt
USE_MOCK_DB=true

# 消息队列配置
MQ_HOST=localhost
MQ_PORT=5672
MQ_USERNAME=guest
MQ_PASSWORD=guest
MQ_EXCHANGE=wechat_messages
USE_MOCK_MQ=true

# 应用配置
APP_NAME=WechatAPI
APP_VERSION=1.0.0
APP_DEBUG=true
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 健康检查配置
HEALTH_CHECK_INTERVAL=60
STATS_OUTPUT_INTERVAL=300
ACCOUNT_REFRESH_INTERVAL=300

# 故障转移配置
FAILOVER_MIN_SWITCH_INTERVAL=60
FAILOVER_MAX_ERROR_COUNT=5

# 并发配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# 微信API配置（继承现有配置）
API_VERSION=v3
GEWE_TOKEN=your_gewe_token
GEWE_APPID=your_gewe_appid
V3_BASE_URL=http://api.geweapi.com/gewe/v2/api
"""


def create_env_file(filename: str = ".env"):
    """创建示例环境变量文件"""
    if not os.path.exists(filename):
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(ENV_EXAMPLE)
        print(f"已创建示例配置文件: {filename}")
        return True
    else:
        print(f"配置文件已存在: {filename}")
        return False


if __name__ == "__main__":
    # 创建示例配置文件
    create_env_file()
    
    # 显示当前配置
    current_config = get_config()
    print("当前配置:")
    print(f"  数据库: {current_config.database.db_host}:{current_config.database.db_port}")
    print(f"  消息队列: {current_config.message_queue.mq_host}:{current_config.message_queue.mq_port}")
    print(f"  使用模拟数据库: {current_config.should_use_mock_db()}")
    print(f"  使用模拟消息队列: {current_config.should_use_mock_mq()}")
    print(f"  日志级别: {current_config.app.log_level}")
