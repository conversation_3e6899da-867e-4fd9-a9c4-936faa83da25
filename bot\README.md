# 微信API多账号管理应用程序

基于现有微信API适配器框架构建的完整应用程序，实现多组账号管理、故障转移和消息队列处理。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息队列      │    │   应用程序      │    │   数据库        │
│   (RabbitMQ)    │◄──►│   (app.py)      │◄──►│   (MySQL)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   组管理器      │
                    │ (GroupManager)  │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   组适配器      │
                    │ (GroupAdapter)  │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  多账号适配器   │
                    │(MultiAccountAdapter)│
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  微信API适配器  │
                    │ (V1/V2/V3)      │
                    └─────────────────┘
```

## 📁 项目结构

```
bot/
├── app.py                    # 主应用程序入口
├── start_yba.py             # YBA组专用启动脚本
├── test_yba_group.py        # YBA组测试脚本
├── config.py                # 配置管理
├── database.py              # 数据库管理器
├── group_adapter.py         # 组适配器
├── group_manager.py         # 组管理器
├── message_queue.py         # 消息队列管理器
├── send_adapter/            # 微信API适配器框架
│   ├── account.py           # 账号对象管理
│   ├── factory.py           # 适配器工厂
│   ├── multi_account_adapter.py # 多账号适配器
│   ├── v1_adapter.py        # V1 API适配器
│   ├── v2_adapter.py        # V2 API适配器
│   ├── v3_adapter.py        # V3 API适配器
│   └── examples/            # 使用示例
└── logs/                    # 日志目录
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install pymysql pika loguru pydantic

# 创建日志目录
mkdir -p logs
```

### 2. 配置数据库

数据库表结构（agents表）：
```sql
CREATE TABLE `agents` ( 
  `id` int NOT NULL AUTO_INCREMENT, 
  `group` varchar(50) DEFAULT NULL COMMENT '代理商分组', 
  `name` varchar(50) NOT NULL COMMENT '代理名称', 
  `contact` varchar(100) DEFAULT NULL COMMENT '联系方式', 
  `token` varchar(64) DEFAULT NULL COMMENT '登录令牌', 
  `wechat_base_url` varchar(255) DEFAULT NULL COMMENT '微信API基础URL', 
  `wechat_is_active` tinyint(1) DEFAULT '0' COMMENT '是否在微信系统中激活', 
  `wechat_login_status` int DEFAULT '0' COMMENT '微信登录状态，0未登录，1已登录', 
  `api_version` varchar(10) DEFAULT 'v1' COMMENT 'API版本：v1/v2/v3', 
  `wxid` varchar(100) DEFAULT NULL COMMENT '微信ID', 
  `app_id` varchar(100) DEFAULT NULL COMMENT 'V3 API应用ID', 
  PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. 启动方式

#### 方式一：启动指定组（推荐）
```bash
# 启动YBA组服务
python start_yba.py

# 或使用通用启动方式
python app.py --group YBA --mock-db --mock-mq
```

#### 方式二：启动所有组
```bash
# 启动所有组的服务
python app.py --mock-db --mock-mq
```

#### 方式三：生产环境启动
```bash
# 使用真实数据库和消息队列
python app.py --group YBA --log-level INFO
```

### 4. 测试功能

```bash
# 测试YBA组功能
python test_yba_group.py

# 测试发送适配器
cd send_adapter/examples
python migration_example.py
```

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=yba_ppmt
USE_MOCK_DB=true

# 消息队列配置
MQ_HOST=localhost
MQ_PORT=5672
MQ_USERNAME=guest
MQ_PASSWORD=guest
MQ_EXCHANGE=wechat_messages
USE_MOCK_MQ=true

# 应用配置
LOG_LEVEL=INFO
HEALTH_CHECK_INTERVAL=60
ACCOUNT_REFRESH_INTERVAL=300
```

### API认证配置

不同版本API的认证方式：

- **V1 API**: 使用 `token` 字段
- **V2 API**: 使用 `wxid` 字段（不需要token）
- **V3 API**: 使用 `token`（作为gewe_token）+ `app_id`（作为gewe_appid）

## 📊 功能特性

### 1. 多账号故障转移
- 自动选择优先级最高的可用账号
- 智能故障检测和切换
- 支持不同API版本混合使用
- 实时状态监控和统计

### 2. 组管理
- 按组隔离账号管理
- 独立的消息队列订阅
- 组级别的健康检查
- 灵活的组配置管理

### 3. 消息队列处理
- 支持RabbitMQ和模拟队列
- 每组独立的队列和路由
- 消息持久化和确认机制
- 并发消息处理

### 4. 数据库集成
- 动态加载账号配置
- 实时状态同步
- 连接池管理
- 模拟数据支持

### 5. 监控和日志
- 详细的运行统计
- 分级日志记录
- 健康状态监控
- 性能指标收集

## 🎯 使用场景

### 1. 单组服务
适用于专门服务某个特定组的场景：
```bash
python start_yba.py
```

### 2. 多组服务
适用于需要同时服务多个组的场景：
```bash
python app.py --mock-db --mock-mq
```

### 3. 开发测试
使用模拟数据进行开发和测试：
```bash
python test_yba_group.py
```

## 📈 性能优化

### 1. 账号选择策略
- 按ID升序排列（ID越小优先级越高）
- 考虑账号成功率和状态
- 避免频繁切换（最小间隔60秒）

### 2. 故障检测机制
- 连续错误次数阈值（默认5次）
- 区分4xx和5xx错误
- 智能重试和退避策略

### 3. 资源管理
- 数据库连接池
- 消息队列连接复用
- 内存使用优化
- 线程池管理

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   python -c "from database import DatabaseManager; db = DatabaseManager(use_mock=False)"
   ```

2. **消息队列连接失败**
   ```bash
   # 使用模拟队列
   python app.py --group YBA --mock-mq
   ```

3. **账号不可用**
   ```bash
   # 检查账号配置
   python test_yba_group.py
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   python app.py --group YBA --log-level DEBUG
   ```

2. **查看账号状态**
   ```python
   from group_adapter import GroupAdapter
   from database import DatabaseManager
   
   db = DatabaseManager(use_mock=True)
   adapter = GroupAdapter("YBA", db)
   print(adapter.get_stats())
   ```

3. **监控消息处理**
   - 查看日志文件：`logs/yba_group.log`
   - 监控统计输出
   - 检查健康检查结果

## 🔄 部署建议

### 开发环境
```bash
# 使用模拟数据
python start_yba.py
```

### 测试环境
```bash
# 使用真实数据库，模拟消息队列
python app.py --group YBA --mock-mq
```

### 生产环境
```bash
# 使用真实数据库和消息队列
python app.py --group YBA --log-level INFO
```

### 服务化部署
```bash
# 使用systemd或supervisor管理服务
# 配置自动重启和日志轮转
# 设置监控和告警
```

## 📝 API接口

### 消息格式

发送文本消息：
```json
{
  "type": "send_text",
  "to_wxid": "target_user_id",
  "content": "消息内容",
  "at": ["user1", "user2"]
}
```

发送图片消息：
```json
{
  "type": "send_image",
  "to_wxid": "target_user_id",
  "image_url": "https://example.com/image.jpg"
}
```

健康检查：
```json
{
  "type": "health_check"
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 支持

如有问题或建议，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查配置文件
4. 提交Issue
