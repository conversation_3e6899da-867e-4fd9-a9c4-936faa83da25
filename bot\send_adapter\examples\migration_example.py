#!/usr/bin/env python3
"""
新旧接口迁移示例
展示如何从传统配置方式迁移到基于账号对象的新方式
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter, create_multi_account_adapter
from send_adapter.models import SendTextRequest
from send_adapter.account import WechatAccount


def old_way_example():
    """旧方式：直接传入配置参数"""
    print("📜 旧方式：直接传入配置参数")
    print("-" * 40)
    
    # 旧方式：直接传入配置
    adapter = create_adapter("v3", 
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"
    )
    
    request = SendTextRequest(
        wxid="wxid_yba********",
        to_wxid="YBA-********",
        content="Hello from old way! 📜"
    )
    
    try:
        response = adapter.send_text(request)
        if response.ok:
            print("✅ 旧方式发送成功!")
            return True
        else:
            print(f"❌ 旧方式发送失败: {response.message}")
            return False
    except Exception as e:
        print(f"❌ 旧方式错误: {e}")
        return False


def new_way_single_account():
    """新方式：使用单个账号对象"""
    print("\n🚀 新方式：使用账号对象")
    print("-" * 40)
    
    # 新方式：创建账号对象
    account = WechatAccount.create_v3_account(
        account_id="yba_main",
        account_name="YBA主账号",
        wxid="wxid_yba********",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl",
        priority=1
    )
    
    print(f"📱 账号信息: {account.account_name}")
    print(f"🔧 API版本: {account.api_version}")
    print(f"🎯 优先级: {account.priority}")
    
    # 使用账号对象创建适配器
    adapter = create_adapter(account)
    
    request = SendTextRequest(
        wxid=account.wxid,  # 使用账号的wxid
        to_wxid="YBA-********",
        content="Hello from new account-based way! 🚀"
    )
    
    try:
        response = adapter.send_text(request)
        if response.ok:
            print("✅ 新方式发送成功!")
            account.mark_success()
            print(f"📊 账号成功率: {account.get_success_rate():.2%}")
            return True, account
        else:
            print(f"❌ 新方式发送失败: {response.message}")
            account.mark_error()
            return False, account
    except Exception as e:
        print(f"❌ 新方式错误: {e}")
        account.mark_error()
        return False, account


def new_way_multi_account():
    """新方式：多账号故障转移"""
    print("\n🔄 新方式：多账号故障转移")
    print("-" * 40)
    
    # 创建多个账号
    accounts = []
    
    # 主账号 (V3)
    main_account = WechatAccount.create_v3_account(
        account_id="main_v3",
        account_name="V3主账号",
        wxid="wxid_yba********",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl",
        priority=1
    )
    accounts.append(main_account)
    
    # 备用账号 (V1) - 示例，实际使用时需要有效配置
    backup_v1 = WechatAccount.create_v1_account(
        account_id="backup_v1",
        account_name="V1备用账号",
        wxid="wxid_backup_v1",
        base_url="http://8.133.252.208:8080",
        api_key="your_v1_key",
        priority=2
    )
    # 设置为不可用，仅作演示
    backup_v1.status = "inactive"
    accounts.append(backup_v1)
    
    # 备用账号 (V2) - 示例，实际使用时需要有效配置
    backup_v2 = WechatAccount.create_v2_account(
        account_id="backup_v2",
        account_name="V2备用账号",
        wxid="wxid_backup_v2",
        base_url="http://8.133.252.208:8060/api",
        api_key="your_v2_key",
        priority=3
    )
    # 设置为不可用，仅作演示
    backup_v2.status = "inactive"
    accounts.append(backup_v2)
    
    print("📋 配置的账号:")
    for acc in accounts:
        print(f"  - {acc.account_name}: {acc.api_version} (优先级: {acc.priority}, 状态: {acc.status})")
    
    # 创建多账号适配器
    multi_adapter = create_multi_account_adapter(accounts)
    
    available_count = len(multi_adapter._get_available_accounts())
    print(f"\n🎯 可用账号数: {available_count}")
    
    # 发送消息（自动选择最佳账号）
    request = SendTextRequest(
        wxid="auto_select",  # 会被自动选择的账号覆盖
        to_wxid="YBA-********",
        content="Hello from multi-account adapter! 🔄"
    )
    
    try:
        response = multi_adapter.send_text(request)
        if response.ok:
            print("✅ 多账号发送成功!")
            
            # 显示使用的账号
            stats = multi_adapter.get_account_stats()
            current_account = stats.get('current_account')
            print(f"🎯 使用账号: {current_account}")
            
            return True, multi_adapter
        else:
            print(f"❌ 多账号发送失败: {response.message}")
            return False, multi_adapter
    except Exception as e:
        print(f"❌ 多账号错误: {e}")
        return False, multi_adapter


def compare_approaches():
    """对比新旧方式的差异"""
    print("\n📊 新旧方式对比")
    print("-" * 40)
    
    comparison = [
        ("配置管理", "分散在代码中", "集中在账号对象"),
        ("多账号支持", "需要手动实现", "内置故障转移"),
        ("统计监控", "无", "内置成功率统计"),
        ("状态管理", "无", "支持账号状态"),
        ("优先级", "无", "支持优先级排序"),
        ("扩展性", "有限", "高度可扩展"),
        ("维护性", "复杂", "简单清晰"),
    ]
    
    print(f"{'特性':<12} {'旧方式':<16} {'新方式'}")
    print("-" * 50)
    for feature, old, new in comparison:
        print(f"{feature:<12} {old:<16} {new}")


def migration_guide():
    """迁移指南"""
    print("\n📖 迁移指南")
    print("-" * 40)
    
    print("🔄 从旧方式迁移到新方式:")
    print()
    
    print("1️⃣ 旧代码:")
    print("```python")
    print("adapter = create_adapter('v3',")
    print("    v3_base_url='http://api.example.com',")
    print("    gewe_token='token',")
    print("    gewe_appid='appid')")
    print("```")
    print()
    
    print("2️⃣ 新代码:")
    print("```python")
    print("account = WechatAccount.create_v3_account(")
    print("    account_id='my_account',")
    print("    account_name='我的账号',")
    print("    wxid='wxid_xxx',")
    print("    base_url='http://api.example.com',")
    print("    gewe_token='token',")
    print("    gewe_appid='appid')")
    print("adapter = create_adapter(account)")
    print("```")
    print()
    
    print("3️⃣ 多账号故障转移:")
    print("```python")
    print("accounts = [account1, account2, account3]")
    print("multi_adapter = create_multi_account_adapter(accounts)")
    print("# 自动故障转移，无需修改业务代码")
    print("response = multi_adapter.send_text(request)")
    print("```")


def main():
    """主函数"""
    print("🔄 新旧接口迁移示例")
    print("=" * 50)
    
    # 1. 旧方式演示
    old_success = old_way_example()
    
    # 2. 新方式单账号演示
    new_success, account = new_way_single_account()
    
    # 3. 新方式多账号演示
    multi_success, multi_adapter = new_way_multi_account()
    
    # 4. 对比分析
    compare_approaches()
    
    # 5. 迁移指南
    migration_guide()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  - 旧方式: {'✅ 成功' if old_success else '❌ 失败'}")
    print(f"  - 新方式(单账号): {'✅ 成功' if new_success else '❌ 失败'}")
    print(f"  - 新方式(多账号): {'✅ 成功' if multi_success else '❌ 失败'}")
    
    if new_success and account:
        print(f"\n📈 账号统计:")
        print(f"  - 成功次数: {account.success_count}")
        print(f"  - 错误次数: {account.error_count}")
        print(f"  - 成功率: {account.get_success_rate():.2%}")
    
    print("\n🎉 迁移演示完成!")
    print("\n💡 建议:")
    print("- 新项目直接使用账号对象方式")
    print("- 现有项目可以逐步迁移")
    print("- 生产环境建议配置多账号故障转移")
    print("- 定期监控账号状态和成功率")


if __name__ == "__main__":
    main()
