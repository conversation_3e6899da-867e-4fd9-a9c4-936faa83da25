"""统一接口抽象层定义
"""
from __future__ import annotations
from abc import ABC, abstractmethod

from .models import (
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest, APIResponse
)


class WechatAPIAdapter(ABC):
    """微信API统一适配器接口（抽象基类）"""

    # ---- 消息发送 ----
    @abstractmethod
    def send_text(self, req: SendTextRequest) -> APIResponse:
        """发送文本（支持@）"""

    @abstractmethod
    def send_image(self, req: SendImageRequest) -> APIResponse:
        """发送图片"""

    @abstractmethod
    def send_app(self, req: SendAppRequest) -> APIResponse:
        """发送app消息（小程序卡片/链接）"""

    @abstractmethod
    def send_voice(self, req: SendVoiceRequest) -> APIResponse:
        """发送语音"""

    @abstractmethod
    def share_card(self, req: ShareCardRequest) -> APIResponse:
        """分享名片"""

    @abstractmethod
    def revoke_message(self, req: RevokeMessageRequest) -> APIResponse:
        """撤回消息"""

    # ---- 转发 ----
    @abstractmethod
    def forward_mini_app(self, req):
        """转发小程序（统一接口）。"""
        pass

    # ---- 文件处理 ----
    @abstractmethod
    def upload_image_to_cdn(self, req: UploadImageToCDNRequest) -> APIResponse:
        """上传图片到CDN（如果目标版本支持）"""

    # ---- 好友管理 ----
    @abstractmethod
    def accept_friend(self, req: AcceptFriendRequest) -> APIResponse:
        """同意好友请求"""

    @abstractmethod
    def get_user_openid(self, req: GetUserOpenidRequest) -> APIResponse:
        """获取用户OpenID"""

    # ---- 群组管理 ----
    @abstractmethod
    def invite_group_member(self, req: InviteGroupMemberRequest) -> APIResponse:
        pass

    @abstractmethod
    def remove_group_member(self, req: RemoveGroupMemberRequest) -> APIResponse:
        pass

    @abstractmethod
    def get_group_info(self, req: GetGroupInfoRequest) -> APIResponse:
        pass

    @abstractmethod
    def get_group_member_detail(self, req: GetGroupMembersRequest) -> APIResponse:
        pass

    # ---- 支付 ----
    @abstractmethod
    def confirm_collection(self, req: ConfirmCollectionRequest) -> APIResponse:
        pass

    # ---- 状态 ----
    @abstractmethod
    def get_online_status(self, req: GetOnlineStatusRequest) -> APIResponse:
        pass

    # ---- V3 扩展能力（按需实现于 V3 适配器）----
    def send_mini_app_v3(self, req):  # type: ignore[override]
        """发送小程序（V3专用）。默认不强制要求其他版本实现。"""
        raise NotImplementedError

    def forward_mini_app_v3(self, req):  # type: ignore[override]
        """转发小程序（V3专用）。"""
        raise NotImplementedError

    def revoke_message_v3(self, req):  # type: ignore[override]
        """撤回消息（V3专用字段）。"""
        raise NotImplementedError

