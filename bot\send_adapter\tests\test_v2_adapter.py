import os
import json
import time
from unittest.mock import patch, MagicMock
import pytest

from send_adapter.config import SendSettings
from send_adapter.factory import create_adapter
from send_adapter.models import (
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest,
)


@pytest.fixture(autouse=True)
def setup_env(monkeypatch):
    monkeypatch.setenv("API_VERSION", "v2")


def test_send_text_success():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        mock_resp = MagicMock()
        mock_resp.status_code = 200
        mock_resp.json.return_value = {"ok": True}
        mock_post.return_value = mock_resp

        resp = adapter.send_text(SendTextRequest(wxid="me", to_wxid="you", content="hi", at=["a","b"]))
        assert resp.ok
        assert mock_post.call_args[0][0].endswith("/Msg/SendTxt")
        sent = mock_post.call_args[1]["json"]
        assert sent["Type"] == 1
        assert sent["At"] == "a,b"


def test_send_image_retry_then_success():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        bad = MagicMock(); bad.status_code = 500; bad.text = "err"
        good = MagicMock(); good.status_code = 200; good.json.return_value = {"ok": True}
        mock_post.side_effect = [bad, good]

        resp = adapter.send_image(SendImageRequest(wxid="me", to_wxid="you", image_base64="xx"))
        assert resp.ok
        assert mock_post.call_count == 2


def test_send_app_http_400_no_retry():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        bad = MagicMock(); bad.status_code = 400; bad.text = "bad"
        mock_post.return_value = bad
        with pytest.raises(Exception):
            adapter.send_app(SendAppRequest(wxid="me", to_wxid="you", xml="<xml/>", type=5))
        assert mock_post.call_count == 1


def test_send_voice_params():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.send_voice(SendVoiceRequest(wxid="me", to_wxid="you", voice_base64="b64", codec_type=4, voice_time_ms=2000))
        payload = mock_post.call_args[1]["json"]
        assert payload["VoiceTime"] == 2000


def test_share_card_optional_fields():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.share_card(ShareCardRequest(wxid="me", to_wxid="you", card_wxid="c", card_nickname="nn"))
        payload = mock_post.call_args[1]["json"]
        assert payload.get("CardNickName") == "nn"
        assert "CardAlias" not in payload


def test_revoke_message():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.revoke_message(RevokeMessageRequest(wxid="me", to_user_name="u", client_msg_id=1, create_time=2, new_msg_id=3))
        assert mock_post.call_args[0][0].endswith("/Msg/Revoke")


def test_accept_friend_and_get_user_openid():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.accept_friend(AcceptFriendRequest(wxid="me", v1="v1", v2="v2", scene=3))
        payload = mock_post.call_args[1]["json"]
        assert payload["Scene"] == 3

        adapter.get_user_openid(GetUserOpenidRequest(wxid="me", appid="appid", to_wxid="u"))
        payload2 = mock_post.call_args[1]["json"]
        assert payload2["Appid"] == "appid"


def test_group_ops():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.invite_group_member(InviteGroupMemberRequest(wxid="me", chatroom_name="room", to_wxids=["a","b"]))
        payload = mock_post.call_args[1]["json"]
        assert payload["ToWxids"] == "a,b"

        adapter.remove_group_member(RemoveGroupMemberRequest(wxid="me", chatroom_name="room", to_wxids=["a"]))
        payload = mock_post.call_args[1]["json"]
        assert payload["ChatRoomName"] == "room"

        adapter.get_group_info(GetGroupInfoRequest(wxid="me", qid="123"))
        assert mock_post.call_args[0][0].endswith("/Group/GetChatRoomInfoDetail")

        adapter.get_group_member_detail(GetGroupMembersRequest(wxid="me", qid="123"))
        assert mock_post.call_args[0][0].endswith("/Group/GetChatRoomMemberDetail")


def test_confirm_collection_and_online_status():
    adapter = create_adapter("v2")
    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.confirm_collection(ConfirmCollectionRequest(wxid="me", to_user_name="u", transfer_id="t", transaction_id="tx"))
        assert mock_post.call_args[0][0].endswith("/TenPay/Collectmoney")

        adapter.get_online_status(GetOnlineStatusRequest(wxid="me"))
        # 该接口使用query参数
        assert mock_post.call_args[0][0].endswith("/Login/GetCacheInfo")
        assert mock_post.call_args[1]["params"]["wxid"] == "me"

