"""适配器工厂，根据账号对象创建对应版本实现"""
from typing import Literal, Optional, Union
from loguru import logger

from .config import send_settings
from .base import WechatAPIAdapter
from .account import WechatAccount, APIVersion


def create_adapter(
    account_or_version: Union[WechatAccount, Literal["v1", "v2", "v3"], None] = None,
    **config
) -> WechatAPIAdapter:
    """创建微信API适配器

    Args:
        account_or_version: 账号对象或API版本字符串
            - 如果传入 WechatAccount 对象，将使用账号的配置
            - 如果传入版本字符串，将使用传统方式创建
            - 如果为 None，使用默认配置
        **config: 额外配置参数（仅在传入版本字符串时使用）

    Returns:
        WechatAPIAdapter: 对应版本的适配器实例
    """

    # 处理账号对象
    if isinstance(account_or_version, WechatAccount):
        account = account_or_version
        version = account.api_version if isinstance(account.api_version, str) else account.api_version.value
        adapter_config = account.to_adapter_config()
        logger.info(f"Creating adapter for account: {account.account_id} (version: {version})")

        # 创建适配器
        if version == "v1":
            from .v1_adapter import WechatAPIV1Adapter
            return WechatAPIV1Adapter(account=account, **adapter_config)
        elif version == "v2":
            from .v2_adapter import WechatAPIV2Adapter
            return WechatAPIV2Adapter(account=account, **adapter_config)
        elif version == "v3":
            from .v3_adapter import WechatAPIV3Adapter
            return WechatAPIV3Adapter(account=account, **adapter_config)
        else:
            raise ValueError(f"Unsupported API version: {version}")

    # 处理版本字符串（向后兼容）
    else:
        version = account_or_version or send_settings.api_version
        logger.info(f"Creating WechatAPIAdapter for version: {version}")

        if version == "v2":
            from .v2_adapter import WechatAPIV2Adapter
            return WechatAPIV2Adapter(**config)
        elif version == "v1":
            from .v1_adapter import WechatAPIV1Adapter
            return WechatAPIV1Adapter(**config)
        elif version == "v3":
            from .v3_adapter import WechatAPIV3Adapter
            return WechatAPIV3Adapter(**config)
        else:
            raise ValueError(f"Unsupported API version: {version}")


def create_adapter_from_account(account: WechatAccount) -> WechatAPIAdapter:
    """从账号对象创建适配器（推荐使用）"""
    return create_adapter(account)


def create_multi_account_adapter(accounts: list[WechatAccount]) -> "MultiAccountAdapter":
    """创建多账号适配器，支持故障转移"""
    from .multi_account_adapter import MultiAccountAdapter
    return MultiAccountAdapter(accounts)

