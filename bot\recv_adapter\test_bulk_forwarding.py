#!/usr/bin/env python3
"""
大量转发性能测试
测试统一MQ发布器在大量未处理消息转发场景下的性能
"""
import asyncio
import time
from datetime import datetime
from typing import List
from unittest.mock import MagicMock
from framework.core.models import StandardMessage

class MockUnifiedMQPublisher:
    """模拟统一MQ发布器 - 性能测试版"""
    
    def __init__(self):
        self.connection = None
        self.channel = None
        self.published_count = 0
        self.total_time = 0
        
    async def connect(self):
        """模拟连接"""
        print("✅ Mock: Unified MQ publisher connected for bulk testing")
        self.connection = MagicMock()
        self.channel = MagicMock()
        
    async def disconnect(self):
        """模拟断开连接"""
        print("✅ Mock: Unified MQ publisher disconnected")
        
    async def publish_unhandled_message(self, message_data: dict, wxid: str) -> bool:
        """模拟发布未处理消息 - 性能优化版"""
        start_time = time.perf_counter()
        
        try:
            # 模拟队列操作的时间开销
            await asyncio.sleep(0.001)  # 1ms模拟网络延迟
            
            self.published_count += 1
            end_time = time.perf_counter()
            self.total_time += (end_time - start_time)
            
            return True
            
        except Exception as e:
            print(f"❌ Mock: Failed to publish message: {e}")
            return False
    
    def get_stats(self):
        """获取性能统计"""
        avg_time = self.total_time / self.published_count if self.published_count > 0 else 0
        return {
            "published_count": self.published_count,
            "total_time": self.total_time,
            "avg_time_per_message": avg_time,
            "messages_per_second": self.published_count / self.total_time if self.total_time > 0 else 0
        }

async def generate_test_messages(count: int, wxid_count: int = 10) -> List[StandardMessage]:
    """生成测试消息"""
    messages = []
    
    for i in range(count):
        wxid = f"wxid_user_{i % wxid_count:03d}"  # 循环使用wxid
        
        message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=10000 + i,
            msg_type=1,
            timestamp=int(datetime.now().timestamp()),
            is_self_message=False,
            wxid=wxid,
            uuid=f"test_uuid_{i}",
            from_user_name=f"test_user_{i}",
            to_user_name="bot",
            content=f"测试消息 #{i+1}",
            push_content="",
            msg_source="",
            ver=1
        )
        messages.append(message)
    
    return messages

async def test_bulk_forwarding(message_count: int = 1000, wxid_count: int = 10):
    """测试大量转发性能"""
    
    print(f"🚀 Testing bulk forwarding performance")
    print(f"   Messages: {message_count}")
    print(f"   Unique WXIDs: {wxid_count}")
    print(f"   Messages per WXID: {message_count // wxid_count}")
    print("=" * 60)
    
    # 创建模拟发布器
    publisher = MockUnifiedMQPublisher()
    await publisher.connect()
    
    # 生成测试消息
    print("📨 Generating test messages...")
    messages = await generate_test_messages(message_count, wxid_count)
    print(f"✅ Generated {len(messages)} test messages")
    
    # 开始批量转发测试
    print("\n🔄 Starting bulk forwarding test...")
    start_time = time.perf_counter()
    
    # 批量处理消息
    batch_size = 100
    successful_count = 0
    failed_count = 0
    
    for i in range(0, len(messages), batch_size):
        batch = messages[i:i + batch_size]
        batch_start = time.perf_counter()
        
        # 并发处理批次
        tasks = []
        for message in batch:
            message_data = message.model_dump(exclude_none=True)
            task = publisher.publish_unhandled_message(message_data, message.wxid)
            tasks.append(task)
        
        # 等待批次完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        batch_successful = sum(1 for r in results if r is True)
        batch_failed = len(results) - batch_successful
        successful_count += batch_successful
        failed_count += batch_failed
        
        batch_end = time.perf_counter()
        batch_time = batch_end - batch_start
        
        # 显示进度
        progress = (i + len(batch)) / len(messages) * 100
        print(f"   Batch {i//batch_size + 1}: {len(batch)} messages, "
              f"{batch_time:.3f}s, {len(batch)/batch_time:.1f} msg/s, "
              f"Progress: {progress:.1f}%")
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    
    # 获取发布器统计
    stats = publisher.get_stats()
    
    # 显示结果
    print("\n📊 Bulk Forwarding Test Results:")
    print("=" * 60)
    print(f"Total Messages: {message_count}")
    print(f"Successful: {successful_count}")
    print(f"Failed: {failed_count}")
    print(f"Success Rate: {successful_count/message_count*100:.2f}%")
    print(f"Total Time: {total_time:.3f} seconds")
    print(f"Messages/Second: {message_count/total_time:.1f}")
    print(f"Average Time per Message: {stats['avg_time_per_message']*1000:.2f} ms")
    
    # 按WXID统计
    wxid_stats = {}
    for message in messages:
        wxid = message.wxid
        if wxid not in wxid_stats:
            wxid_stats[wxid] = 0
        wxid_stats[wxid] += 1
    
    print(f"\n📋 WXID Distribution:")
    for wxid, count in sorted(wxid_stats.items()):
        print(f"   {wxid}: {count} messages")
    
    # 性能评估
    print(f"\n🎯 Performance Assessment:")
    if message_count / total_time > 500:
        print("   ✅ Excellent performance (>500 msg/s)")
    elif message_count / total_time > 200:
        print("   ✅ Good performance (>200 msg/s)")
    elif message_count / total_time > 100:
        print("   ⚠️  Acceptable performance (>100 msg/s)")
    else:
        print("   ❌ Poor performance (<100 msg/s)")
    
    await publisher.disconnect()
    
    return {
        "total_messages": message_count,
        "successful": successful_count,
        "failed": failed_count,
        "total_time": total_time,
        "messages_per_second": message_count / total_time,
        "avg_time_per_message": stats['avg_time_per_message']
    }

async def run_performance_tests():
    """运行多个性能测试"""
    
    print("🧪 Bulk Forwarding Performance Tests")
    print("=" * 70)
    
    test_cases = [
        {"messages": 100, "wxids": 5, "name": "Small Load"},
        {"messages": 1000, "wxids": 10, "name": "Medium Load"},
        {"messages": 5000, "wxids": 20, "name": "Large Load"},
        {"messages": 10000, "wxids": 50, "name": "Very Large Load"},
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔬 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        result = await test_bulk_forwarding(
            message_count=test_case["messages"],
            wxid_count=test_case["wxids"]
        )
        
        result["test_name"] = test_case["name"]
        results.append(result)
        
        # 短暂休息
        await asyncio.sleep(1)
    
    # 总结报告
    print("\n📈 Performance Summary Report")
    print("=" * 70)
    print(f"{'Test Name':<20} {'Messages':<10} {'Time(s)':<10} {'Msg/s':<10} {'Success%':<10}")
    print("-" * 70)
    
    for result in results:
        success_rate = result["successful"] / result["total_messages"] * 100
        print(f"{result['test_name']:<20} "
              f"{result['total_messages']:<10} "
              f"{result['total_time']:<10.2f} "
              f"{result['messages_per_second']:<10.1f} "
              f"{success_rate:<10.1f}")
    
    print("\n✅ All performance tests completed!")

async def main():
    """主函数"""
    await run_performance_tests()

if __name__ == "__main__":
    asyncio.run(main())
