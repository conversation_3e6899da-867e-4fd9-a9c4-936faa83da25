#!/usr/bin/env python3
"""
发送消息给 YBA-19990312 的完整示例
演示如何使用 V3 接口发送各种类型的消息
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import (
    SendTextRequest, SendImageRequest,
    ShareCardRequest, ForwardMiniAppRequest
)


def create_v3_adapter():
    """创建配置好的 V3 适配器"""
    return create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",    # 🔑 替换为你的实际 Token
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"         # 🔑 替换为你的实际 App ID
    )


def send_text_to_yba():
    """发送文本消息给 YBA-19990312"""
    print("📝 发送文本消息...")
    
    adapter = create_v3_adapter()
    
    request = SendTextRequest(
        wxid="wxid_yba19990312",        # 🤖 替换为你的机器人微信ID
        to_wxid="YBA-19990312",
        content="Hi YBA! 这是一条测试消息 👋\n\n来自 V3 接口的问候！"
    )
    
    try:
        response = adapter.send_text(request)
        if response.ok:
            print("✅ 文本消息发送成功!")
            print(f"📋 消息ID: {response.data.get('newMsgId')}")
            return True
        else:
            print(f"❌ 发送失败: {response.message}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def send_image_to_yba():
    """发送图片消息给 YBA-19990312"""
    print("\n🖼️ 发送图片消息...")

    adapter = create_v3_adapter()

    # V3 需要图片 URL，不支持 base64
    # 使用一个真实可访问的测试图片 URL
    image_url = "https://picsum.photos/300/200"  # 随机图片服务，实际可访问

    print(f"� 使用图片URL: {image_url}")

    request = SendImageRequest(
        wxid="wxid_yba19990312",
        to_wxid="YBA-19990312",
        image_base64=image_url  # V3 中这个字段实际传入 URL
    )

    try:
        response = adapter.send_image(request)
        if response.ok:
            print("✅ 图片消息发送成功!")
            return True
        else:
            print(f"❌ 发送失败: {response.message}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("💡 提示: V3 接口需要传入真实可访问的图片 URL")
        print("💡 建议: 使用你自己的图片服务器或对象存储的图片链接")
        return False


def send_voice_to_yba():
    """发送语音消息给 YBA-19990312"""
    print("\n🎵 发送语音消息...")
    print("⚠️ 跳过语音消息发送 - 需要真实的 silk 格式语音文件 URL")
    print("💡 如需测试语音发送，请:")
    print("   1. 准备 silk 格式的语音文件")
    print("   2. 上传到可访问的服务器")
    print("   3. 将 URL 替换到代码中")
    return False  # 暂时跳过，避免错误


def send_app_message_to_yba():
    """发送应用消息给 YBA-19990312"""
    print("\n📱 发送应用消息...")
    print("⚠️ 跳过应用消息发送 - 需要有效的应用消息 XML")
    print("💡 应用消息需要:")
    print("   1. 有效的 appid（需要在微信开放平台注册）")
    print("   2. 正确的 XML 格式")
    print("   3. 可访问的 URL 链接")
    print("   4. 符合微信规范的内容")
    print("💡 建议: 如需测试应用消息，请使用真实的小程序或公众号 XML")
    return False  # 暂时跳过，避免错误


def share_card_to_yba():
    """分享名片给 YBA-19990312"""
    print("\n👤 分享名片...")
    
    adapter = create_v3_adapter()
    
    request = ShareCardRequest(
        wxid="wxid_yba19990312",
        to_wxid="YBA-19990312",
        card_wxid="YBA-19990312",        # 🔗 替换为要分享的人的微信ID
        card_nickname="YBA",             # 要分享的人的昵称
        card_alias="YBA-19990312"        # 要分享的人的备注名（可选）
    )
    
    try:
        response = adapter.share_card(request)
        if response.ok:
            print("✅ 名片分享成功!")
            return True
        else:
            print(f"❌ 分享失败: {response.message}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def forward_mini_app_to_yba():
    """转发小程序给 YBA-19990312"""
    print("\n🎮 转发小程序...")
    
    adapter = create_v3_adapter()
    
    # 示例小程序 XML（实际使用时需要替换为有效的小程序 XML）
    mini_app_xml = """
    <msg>
        <appmsg appid="wx1234567890" sdkver="0">
            <title>测试小程序</title>
            <des>这是一个测试小程序</des>
            <type>33</type>
            <url>https://mp.weixin.qq.com/mp/waerrpage</url>
            <weappinfo>
                <username>gh_1234567890ab</username>
                <appid>wx1234567890</appid>
                <pagepath>pages/index</pagepath>
            </weappinfo>
        </appmsg>
    </msg>
    """
    
    request = ForwardMiniAppRequest(
        wxid="wxid_yba19990312",
        to_wxid="YBA-19990312",
        xml=mini_app_xml.strip(),
        cover_img_url="https://picsum.photos/200/200"  # 小程序封面图 URL
    )
    
    try:
        response = adapter.forward_mini_app(request)
        if response.ok:
            print("✅ 小程序转发成功!")
            return True
        else:
            print(f"❌ 转发失败: {response.message}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def main():
    """主函数"""
    print("🚀 发送消息给 YBA-19990312 示例")
    print("=" * 50)
    
    # 检查配置
    print("⚙️ 配置检查:")
    print("请确保已在代码中设置正确的配置值:")
    print("- gewe_token: 你的 GeWe Token")
    print("- gewe_appid: 你的 App ID") 
    print("- your_bot_wxid: 你的机器人微信ID")
    print()
    
    # 发送各种类型的消息
    results = []
    
    # 1. 发送文本消息
    results.append(("文本消息", send_text_to_yba()))
    
    # 2. 发送图片消息（需要图片 URL）
    results.append(("图片消息", send_image_to_yba()))
    
    # 3. 发送语音消息（需要语音 URL）
    # results.append(("语音消息", send_voice_to_yba()))
    
    # 4. 发送应用消息（暂时跳过）
    # results.append(("应用消息", send_app_message_to_yba()))
    
    # 5. 分享名片
    results.append(("分享名片", share_card_to_yba()))
    
    # 6. 转发小程序
    results.append(("转发小程序", forward_mini_app_to_yba()))
    
    # 显示结果汇总
    print("\n" + "=" * 50)
    print("📊 发送结果汇总:")
    for msg_type, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {msg_type}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    print(f"\n总计: {success_count}/{len(results)} 条消息发送成功")
    
    if success_count == 0:
        print("\n💡 如果所有消息都发送失败，请检查:")
        print("1. GeWe Token 和 App ID 是否正确")
        print("2. 机器人是否已登录")
        print("3. 网络连接是否正常")
        print("4. 目标用户 YBA-19990312 是否存在")


if __name__ == "__main__":
    main()
