"""Pydantic 数据模型定义
以统一接口层为准，尽量不暴露底层API差异。
"""
from typing import List, Optional
from pydantic import BaseModel, Field


# ---- 通用返回 ----
class APIResponse(BaseModel):
    ok: bool = Field(default=True, description="是否成功")
    code: Optional[int] = Field(default=None, description="服务端返回码")
    message: Optional[str] = None
    data: Optional[dict] = None


# ---- 消息发送 ----
class SendTextRequest(BaseModel):
    wxid: str
    to_wxid: str
    content: str
    at: Optional[List[str]] = Field(default=None, description="群@的wxid列表")


class SendImageRequest(BaseModel):
    wxid: str
    to_wxid: str
    image_base64: str


class SendAppRequest(BaseModel):
    wxid: str
    to_wxid: str
    xml: str
    type: int = 5  # 缺省为分享链接/小程序等，由适配器按文档传递


class SendVoiceRequest(BaseModel):
    wxid: str
    to_wxid: str
    voice_base64: str
    codec_type: int = 4  # 缺省SILK=4
    voice_time_ms: int = 1000


class ShareCardRequest(BaseModel):
    wxid: str
    to_wxid: str
    card_wxid: str
    card_nickname: Optional[str] = None
    card_alias: Optional[str] = None


class RevokeMessageRequest(BaseModel):
    wxid: str
    to_user_name: str
    client_msg_id: int
    create_time: int
    new_msg_id: int


# ---- 文件处理 ----
class UploadImageToCDNRequest(BaseModel):
    wxid: str
    base64: str


# ---- 好友管理 ----
class AcceptFriendRequest(BaseModel):
    # 统一模型：兼容 V1/V2 的 v1/v2/scene；兼容 V3 的 scene/option/v3/v4/content
    wxid: str
    # V1/V2 验证字段
    v1: str | None = None
    v2: str | None = None
    scene: int | None = None
    # V3 新增字段
    option: int | None = None
    v3: str | None = None
    v4: str | None = None
    content: str | None = None


class GetUserOpenidRequest(BaseModel):
    wxid: str
    appid: str
    to_wxid: str


# ---- 群组管理 ----
class InviteGroupMemberRequest(BaseModel):
    wxid: str
    chatroom_name: str
    to_wxids: List[str]


class RemoveGroupMemberRequest(BaseModel):
    wxid: str
    chatroom_name: str
    to_wxids: List[str]


class GetGroupInfoRequest(BaseModel):
    wxid: str
    qid: str


class GetGroupMembersRequest(BaseModel):
    wxid: str
    qid: str


# ---- 支付 ----
class ConfirmCollectionRequest(BaseModel):
    wxid: str
    to_user_name: str
    transfer_id: str
    transaction_id: str
    invalid_time: Optional[str] = None


# ---- 状态 ----
class GetOnlineStatusRequest(BaseModel):
    wxid: str


# ---- 统一：转发小程序 ----
class ForwardMiniAppRequest(BaseModel):
    wxid: str
    to_wxid: str
    xml: str
    cover_img_url: Optional[str] = None  # V3 需要；V1/V2 可忽略


# ---- V3 专用模型（严格按 GeWe 文档字段命名）----

class SendTextV3Request(BaseModel):
    """V3 发送文字消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    content: str
    ats: Optional[str] = None  # @的好友，多个英文逗号分隔

    class Config:
        allow_population_by_field_name = True


class SendImageV3Request(BaseModel):
    """V3 发送图片消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    img_url: str = Field(alias="imgUrl")  # 图片链接

    class Config:
        allow_population_by_field_name = True


class SendVoiceV3Request(BaseModel):
    """V3 发送语音消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    voice_url: str = Field(alias="voiceUrl")  # 语音文件链接，仅支持silk格式
    voice_duration: int = Field(alias="voiceDuration")  # 语音时长，单位毫秒

    class Config:
        allow_population_by_field_name = True


class SendAppMsgV3Request(BaseModel):
    """V3 发送appmsg消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    appmsg: str  # 回调消息中的appmsg节点内容

    class Config:
        allow_population_by_field_name = True


class SendMiniAppV3Request(BaseModel):
    """V3 发送小程序消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    mini_app_id: str = Field(alias="miniAppId")
    display_name: str = Field(alias="displayName")
    page_path: str = Field(alias="pagePath")
    cover_img_url: str = Field(alias="coverImgUrl")
    title: str
    user_name: str = Field(alias="userName")

    class Config:
        allow_population_by_field_name = True


class ForwardMiniAppV3Request(BaseModel):
    """V3 转发小程序"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    xml: str  # 文件消息的xml
    cover_img_url: str = Field(alias="coverImgUrl")  # 小程序封面图链接

    class Config:
        allow_population_by_field_name = True


class RevokeMsgV3Request(BaseModel):
    """V3 撤回消息"""
    app_id: str = Field(alias="appId")
    to_wxid: str = Field(alias="toWxid")
    msg_id: str = Field(alias="msgId")  # 发送类接口返回的msgId
    new_msg_id: str = Field(alias="newMsgId")  # 发送类接口返回的newMsgId
    create_time: str = Field(alias="createTime")  # 发送类接口返回的createTime

    class Config:
        allow_population_by_field_name = True

