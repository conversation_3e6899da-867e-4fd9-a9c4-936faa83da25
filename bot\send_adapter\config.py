"""
发送适配器配置管理
使用 Pydantic BaseSettings 读取环境变量，保持与 recv_adapter 一致的风格
"""
from typing import Literal
from pydantic_settings import BaseSettings


class SendSettings(BaseSettings):
    """发送适配器配置"""

    # 选择API版本: v1/v2/v3
    api_version: Literal["v1", "v2", "v3"] = "v2"

    # 各版本基础地址（可通过环境变量覆盖）
    v1_base_url: str = "http://8.133.252.208:8080"  # V1 swagger: /docs/swagger.json
    v2_base_url: str = "http://8.133.252.208:8060/api"  # V2 swagger basePath=/api
    # V3 使用 GeWe 开放平台，默认直接指向 API 基地址（建议包含 /gewe/v2/api 前缀）
    v3_base_url: str = "http://api.geweapi.com/gewe/v2/api"

    # 认证/Key（如有）
    api_key: str | None = None  # V1/V2 通用 key
    # V3 GeWe 平台常用鉴权参数，可通过环境变量注入
    gewe_token: str | None = None  # 对应文档中 gewe-token
    gewe_appid: str | None = None  # appid

    # HTTP 请求配置
    request_timeout: float = 10.0
    max_retries: int = 3
    backoff_factor: float = 0.5  # 指数退避基数，单位秒

    # 日志级别
    log_level: str = "INFO"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例（便于简单用法）
send_settings = SendSettings()

