"""V1 版本微信API适配器实现
参考 swagger: http://8.133.252.208:8080/docs/swagger.json
注意：V1 的路径与字段命名可能与 V2 不同，这里做统一封装。
"""
from __future__ import annotations
import time
from typing import Dict, Any
import requests
from loguru import logger

from .base import WechatAPIAdapter
from .config import send_settings
from .exceptions import APIRequestError, APIServerError
from .models import (
    APIResponse,
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest,
    ForwardMiniAppRequest,
)
from .account import WechatAccount


class WechatAPIV1Adapter(WechatAPIAdapter):
    def __init__(self, account: WechatAccount = None, **config):
        # 支持账号对象或传统配置方式
        if account:
            self.account = account
            self.base_url = account.base_url.rstrip("/")
            self.timeout = account.timeout
            self.max_retries = account.max_retries
            self.backoff_factor = account.backoff_factor
            self.api_key = account.get_api_key()
        else:
            # 向后兼容：支持传入配置参数
            self.account = None
            self.base_url = config.get("v1_base_url", send_settings.v1_base_url).rstrip("/")
            self.timeout = config.get("request_timeout", send_settings.request_timeout)
            self.max_retries = config.get("max_retries", send_settings.max_retries)
            self.backoff_factor = config.get("backoff_factor", send_settings.backoff_factor)
            self.api_key = config.get("v1_key", send_settings.api_key)

    def _post(self, path: str, json: Dict[str, Any] | None = None, params: Dict[str, Any] | None = None) -> APIResponse:
        url = f"{self.base_url}{path}"
        merged_params = dict(params or {})
        if self.api_key:
            merged_params.setdefault("key", self.api_key)
        attempt = 0
        last_exc: Exception | None = None
        while attempt < self.max_retries:
            try:
                logger.debug(f"POST {url} json={json} params={merged_params}")
                resp = requests.post(url, json=json, params=merged_params, timeout=self.timeout)
                if 500 <= resp.status_code < 600:
                    raise APIServerError(f"Server error {resp.status_code}", status_code=resp.status_code)
                if resp.status_code != 200:
                    raise APIRequestError(f"HTTP {resp.status_code}: {resp.text}", status_code=resp.status_code)
                try:
                    payload = resp.json()
                except Exception:
                    payload = {"raw": resp.text}
                return APIResponse(ok=True, code=resp.status_code, data=payload)
            except (requests.Timeout, requests.ConnectionError, APIServerError) as e:
                last_exc = e
                attempt += 1
                sleep_s = self.backoff_factor * (2 ** (attempt - 1))
                logger.warning(f"Request failed (attempt {attempt}/{self.max_retries}): {e}. Backing off {sleep_s:.2f}s")
                time.sleep(sleep_s)
            except APIRequestError as e:
                logger.error(f"Non-retryable HTTP error: {e}")
                raise
            except Exception as e:
                last_exc = e
                logger.error(f"Unexpected error: {e}")
                attempt += 1
                time.sleep(self.backoff_factor)
        raise APIRequestError(f"Request failed after {self.max_retries} attempts: {last_exc}")

    # ---- 消息发送 ----
    def send_text(self, req: SendTextRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToUserName": req.to_wxid,  # V1 可能使用 ToUserName
            "Content": req.content,
            "MsgType": 1,  # 文本
        }
        if req.at:
            # V1 文档未明确At字段，若支持，通常同V2为逗号分隔
            body["At"] = ",".join(req.at)
        return self._post("/message/SendMessage", json={"MsgItem": [body]})

    def send_image(self, req: SendImageRequest) -> APIResponse:
        # V1 兼容接口：/message/SendMessage with MsgType=2 and ImageContent
        body = {
            "Wxid": req.wxid,
            "ToUserName": req.to_wxid,
            "MsgType": 2,
            "ImageContent": req.image_base64,
        }
        return self._post("/message/SendMessage", json={"MsgItem": [body]})

    def send_app(self, req: SendAppRequest) -> APIResponse:
        # V1: 采用 AppMessageModel /message/SendAppMessage 或统一 SendMessage 的App类型
        body = {
            "ToUserName": req.to_wxid,
            "ContentType": req.type,
            "ContentXML": req.xml,
        }
        return self._post("/message/SendAppMessage", json={"AppList": [body]})

    def send_voice(self, req: SendVoiceRequest) -> APIResponse:
        body = {
            "ToUserName": req.to_wxid,
            "VoiceData": req.voice_base64,
            "VoiceFormat": req.codec_type,
            "VoiceSecond": int(max(1, round(req.voice_time_ms / 1000))),
        }
        return self._post("/message/SendUploadVoice", json=body)

    def share_card(self, req: ShareCardRequest) -> APIResponse:
        body = {
            "ToUserName": req.to_wxid,
            "CardWxId": req.card_wxid,
        }
        if req.card_nickname:
            body["CardNickName"] = req.card_nickname
        if req.card_alias:
            body["CardAlias"] = req.card_alias
        return self._post("/message/ShareCard", json=body)

    def revoke_message(self, req: RevokeMessageRequest) -> APIResponse:
        body = {
            "ToUserName": req.to_user_name,
            "ClientMsgId": req.client_msg_id,
            "CreateTime": req.create_time,
            "NewMsgId": req.new_msg_id,
        }
        return self._post("/message/RevokeMsg", json=body)

    def forward_mini_app(self, req: ForwardMiniAppRequest) -> APIResponse:
        # V1 未提供专用接口，复用发送appmsg（XML）
        body = {"ToUserName": req.to_wxid, "AppXml": req.xml}
        return self._post("/message/SendAppMessage", json={"AppList": [body]})

    # ---- 文件处理 ----
    def upload_image_to_cdn(self, req: UploadImageToCDNRequest) -> APIResponse:
        # V1 提供朋友圈上传，或消息图片上传，这里以朋友圈上传图片接口占位
        body = {"Base64": req.base64}
        return self._post("/sns/UploadFriendCircleImage", json=body)

    # ---- 好友管理 ----
    def accept_friend(self, req: AcceptFriendRequest) -> APIResponse:
        body = {"V1": req.v1, "V2": req.v2, "Scene": req.scene}
        return self._post("/friend/PassVerify", json=body)

    def get_user_openid(self, req: GetUserOpenidRequest) -> APIResponse:
        body = {"Appid": req.appid, "ToUserName": req.to_wxid}
        return self._post("/applet/GetUserOpenId", json=body)

    # ---- 群组管理 ----
    def invite_group_member(self, req: InviteGroupMemberRequest) -> APIResponse:
        body = {"ChatRoomName": req.chatroom_name, "UserList": req.to_wxids}
        return self._post("/group/InviteChatroomMembers", json=body)

    def remove_group_member(self, req: RemoveGroupMemberRequest) -> APIResponse:
        body = {"ChatRoomName": req.chatroom_name, "UserList": req.to_wxids}
        return self._post("/group/DelChatRoomMember", json=body)

    def get_group_info(self, req: GetGroupInfoRequest) -> APIResponse:
        body = {"ChatRoomName": req.qid}
        return self._post("/group/GetChatroomInfo", json=body)

    def get_group_member_detail(self, req: GetGroupMembersRequest) -> APIResponse:
        body = {"ChatRoomName": req.qid}
        return self._post("/group/GetChatroomMemberDetail", json=body)

    # ---- 支付 ----
    def confirm_collection(self, req: ConfirmCollectionRequest) -> APIResponse:
        body = {
            "ToUserName": req.to_user_name,
            "TransFerId": req.transfer_id,
            "TransactionId": req.transaction_id,
        }
        return self._post("/pay/Collectmoney", json=body)

    # ---- 状态 ----
    def get_online_status(self, req: GetOnlineStatusRequest) -> APIResponse:
        # V1 可能提供 /login/GetCacheInfo
        return self._post("/login/GetCacheInfo", params={"wxid": req.wxid})

