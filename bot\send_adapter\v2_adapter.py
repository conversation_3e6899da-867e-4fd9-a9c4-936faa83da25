"""V2 版本微信API适配器实现
参考 swagger: http://8.133.252.208:8060/swagger.json (basePath=/api)
"""
from __future__ import annotations
import time
from typing import Dict, Any
import requests
from loguru import logger

from .base import WechatAPIAdapter
from .config import send_settings
from .exceptions import APIRequestError, APIServerError
from .models import (
    APIResponse,
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest,
    ForwardMiniAppRequest,
)
from .account import WechatAccount


class WechatAPIV2Adapter(WechatAPIAdapter):
    def __init__(self, account: WechatAccount = None, **config):
        # 支持账号对象或传统配置方式
        if account:
            self.account = account
            self.base_url = account.base_url.rstrip("/")
            self.timeout = account.timeout
            self.max_retries = account.max_retries
            self.backoff_factor = account.backoff_factor
            self.api_key = account.get_api_key()
        else:
            # 向后兼容：支持传入配置参数
            self.account = None
            self.base_url = config.get("v2_base_url", send_settings.v2_base_url).rstrip("/")
            self.timeout = config.get("request_timeout", send_settings.request_timeout)
            self.max_retries = config.get("max_retries", send_settings.max_retries)
            self.backoff_factor = config.get("backoff_factor", send_settings.backoff_factor)
            self.api_key = config.get("v2_key", send_settings.api_key)

    # 统一请求入口，内置重试、日志、错误处理
    def _post(self, path: str, json: Dict[str, Any] | None = None, params: Dict[str, Any] | None = None) -> APIResponse:
        url = f"{self.base_url}{path}"
        # 合并全局api_key到query参数（若配置提供）
        merged_params = dict(params or {})
        if self.api_key:
            merged_params.setdefault("key", self.api_key)
        attempt = 0
        last_exc: Exception | None = None
        while attempt < self.max_retries:
            try:
                logger.debug(f"POST {url} json={json} params={merged_params}")
                resp = requests.post(url, json=json, params=merged_params, timeout=self.timeout)
                if 500 <= resp.status_code < 600:
                    raise APIServerError(f"Server error {resp.status_code}", status_code=resp.status_code)
                if resp.status_code != 200:
                    raise APIRequestError(f"HTTP {resp.status_code}: {resp.text}", status_code=resp.status_code)
                # 大部分接口返回JSON，这里尽量解析
                try:
                    payload = resp.json()
                except Exception:
                    payload = {"raw": resp.text}
                return APIResponse(ok=True, code=resp.status_code, data=payload)
            except (requests.Timeout, requests.ConnectionError, APIServerError) as e:
                last_exc = e
                attempt += 1
                sleep_s = self.backoff_factor * (2 ** (attempt - 1))
                logger.warning(f"Request failed (attempt {attempt}/{self.max_retries}): {e}. Backing off {sleep_s:.2f}s")
                time.sleep(sleep_s)
            except APIRequestError as e:
                # 4xx 不重试
                logger.error(f"Non-retryable HTTP error: {e}")
                raise
            except Exception as e:
                last_exc = e
                logger.error(f"Unexpected error: {e}")
                attempt += 1
                time.sleep(self.backoff_factor)
        # 超过重试
        raise APIRequestError(f"Request failed after {self.max_retries} attempts: {last_exc}")

    # ---- 消息发送 ----
    def send_text(self, req: SendTextRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToWxid": req.to_wxid,
            "Content": req.content,
            "Type": 1,  # 文本
        }
        if req.at:
            body["At"] = ",".join(req.at)
        return self._post("/Msg/SendTxt", json=body)

    def send_image(self, req: SendImageRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "ToWxid": req.to_wxid, "Base64": req.image_base64}
        return self._post("/Msg/UploadImg", json=body)

    def send_app(self, req: SendAppRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "ToWxid": req.to_wxid, "Type": req.type, "Xml": req.xml}
        return self._post("/Msg/SendApp", json=body)

    def send_voice(self, req: SendVoiceRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToWxid": req.to_wxid,
            "Base64": req.voice_base64,
            "Type": req.codec_type,
            "VoiceTime": req.voice_time_ms,
        }
        return self._post("/Msg/SendVoice", json=body)

    def share_card(self, req: ShareCardRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToWxid": req.to_wxid,
            "CardWxId": req.card_wxid,
        }
        if req.card_nickname:
            body["CardNickName"] = req.card_nickname
        if req.card_alias:
            body["CardAlias"] = req.card_alias
        return self._post("/Msg/ShareCard", json=body)

    def revoke_message(self, req: RevokeMessageRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToUserName": req.to_user_name,
            "ClientMsgId": req.client_msg_id,
            "CreateTime": req.create_time,
            "NewMsgId": req.new_msg_id,
        }
        return self._post("/Msg/Revoke", json=body)

    def forward_mini_app(self, req: ForwardMiniAppRequest) -> APIResponse:
        # V2 没有单独转发小程序，复用发送AppMsg（XML）
        body = {"Wxid": req.wxid, "ToWxid": req.to_wxid, "Type": 33, "Xml": req.xml}
        return self._post("/Msg/SendApp", json=body)

    # ---- 文件处理 ----
    def upload_image_to_cdn(self, req: UploadImageToCDNRequest) -> APIResponse:
        # V2 没有单独“上传图片到CDN”的公开接口，按“朋友圈上传”或“消息发图”即可
        # 这里默认复用发送图片接口以达到CDN托管
        body = {"Wxid": req.wxid, "ToWxid": req.wxid, "Base64": req.base64}
        return self._post("/Msg/UploadImg", json=body)

    # ---- 好友管理 ----
    def accept_friend(self, req: AcceptFriendRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "V1": req.v1, "V2": req.v2, "Scene": req.scene}
        return self._post("/Friend/PassVerify", json=body)

    def get_user_openid(self, req: GetUserOpenidRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "Appid": req.appid, "ToWxId": req.to_wxid}
        return self._post("/Wxapp/GetUserOpenId", json=body)

    # ---- 群组管理 ----
    def invite_group_member(self, req: InviteGroupMemberRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "ChatRoomName": req.chatroom_name, "ToWxids": ",".join(req.to_wxids)}
        return self._post("/Group/InviteChatRoomMember", json=body)

    def remove_group_member(self, req: RemoveGroupMemberRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "ChatRoomName": req.chatroom_name, "ToWxids": ",".join(req.to_wxids)}
        return self._post("/Group/DelChatRoomMember", json=body)

    def get_group_info(self, req: GetGroupInfoRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "QID": req.qid}
        return self._post("/Group/GetChatRoomInfoDetail", json=body)

    def get_group_member_detail(self, req: GetGroupMembersRequest) -> APIResponse:
        body = {"Wxid": req.wxid, "QID": req.qid}
        return self._post("/Group/GetChatRoomMemberDetail", json=body)

    # ---- 支付 ----
    def confirm_collection(self, req: ConfirmCollectionRequest) -> APIResponse:
        body = {
            "Wxid": req.wxid,
            "ToUserName": req.to_user_name,
            "TransFerId": req.transfer_id,
            "TransactionId": req.transaction_id,
        }
        if req.invalid_time:
            body["InvalidTime"] = req.invalid_time
        return self._post("/TenPay/Collectmoney", json=body)

    # ---- 状态 ----
    def get_online_status(self, req: GetOnlineStatusRequest) -> APIResponse:
        # V2 无直接“在线状态”接口，使用登录缓存或心跳来判断
        # 这里尝试调用 /Login/GetCacheInfo?wxid=xxx
        return self._post("/Login/GetCacheInfo", params={"wxid": req.wxid})

