"""
多账号适配器，支持故障转移
"""
import time
from typing import List, Optional, Callable, Any
from loguru import logger

from .base import WechatAPIAdapter
from .account import WechatAccount, AccountStatus
from .factory import create_adapter_from_account
from .models import APIResponse
from .exceptions import APIRequestError, APIServerError


class MultiAccountAdapter(WechatAPIAdapter):
    """多账号适配器，支持自动故障转移"""
    
    def __init__(self, accounts: List[WechatAccount]):
        """
        初始化多账号适配器
        
        Args:
            accounts: 账号列表，按优先级排序
        """
        if not accounts:
            raise ValueError("至少需要一个账号")
        
        self.accounts = sorted(accounts, key=lambda x: (x.priority, -x.get_success_rate()))
        self.adapters = {}  # 缓存适配器实例
        self.current_account = None
        self.last_switch_time = 0
        self.min_switch_interval = 60  # 最小切换间隔（秒）
        
        logger.info(f"初始化多账号适配器，共 {len(accounts)} 个账号")
        for acc in self.accounts:
            logger.info(f"  - {acc.account_id} ({acc.api_version}, 优先级: {acc.priority})")
    
    def _get_adapter(self, account: WechatAccount) -> WechatAPIAdapter:
        """获取账号对应的适配器实例"""
        if account.account_id not in self.adapters:
            self.adapters[account.account_id] = create_adapter_from_account(account)
        return self.adapters[account.account_id]
    
    def _get_available_accounts(self) -> List[WechatAccount]:
        """获取当前可用的账号列表"""
        return [acc for acc in self.accounts if acc.is_available()]
    
    def _select_account(self, exclude_account: WechatAccount = None) -> Optional[WechatAccount]:
        """选择最佳可用账号"""
        available = self._get_available_accounts()
        
        if exclude_account:
            available = [acc for acc in available if acc.account_id != exclude_account.account_id]
        
        if not available:
            logger.warning("没有可用的账号")
            return None
        
        # 返回优先级最高的账号
        selected = available[0]
        logger.info(f"选择账号: {selected.account_id} ({selected.api_version})")
        return selected
    
    def _should_switch_account(self, current_account: WechatAccount) -> bool:
        """判断是否应该切换账号"""
        # 检查当前账号是否仍然可用
        if not current_account.is_available():
            return True
        
        # 检查是否有更高优先级的账号可用
        available = self._get_available_accounts()
        if available and available[0].priority < current_account.priority:
            # 避免频繁切换
            if time.time() - self.last_switch_time > self.min_switch_interval:
                return True
        
        return False
    
    def _execute_with_failover(self, method_name: str, *args, **kwargs) -> APIResponse:
        """执行方法，支持故障转移"""
        last_error = None
        attempted_accounts = set()
        
        while True:
            # 选择账号
            if (not self.current_account or
                self._should_switch_account(self.current_account) or
                (self.current_account and self.current_account.account_id in attempted_accounts)):

                exclude_account = None
                if self.current_account and self.current_account.account_id in attempted_accounts:
                    exclude_account = self.current_account

                self.current_account = self._select_account(exclude_account=exclude_account)

                if not self.current_account:
                    break

                self.last_switch_time = time.time()
                logger.info(f"切换到账号: {self.current_account.account_id}")

            # 如果已经尝试过这个账号，跳出循环
            if self.current_account and self.current_account.account_id in attempted_accounts:
                break

            if self.current_account:
                attempted_accounts.add(self.current_account.account_id)
            
            try:
                # 获取适配器并执行方法
                adapter = self._get_adapter(self.current_account)
                method = getattr(adapter, method_name)
                
                logger.debug(f"使用账号 {self.current_account.account_id} 执行 {method_name}")
                response = method(*args, **kwargs)
                
                # 标记成功
                self.current_account.mark_success()
                
                return response
                
            except (APIRequestError, APIServerError) as e:
                last_error = e
                logger.warning(f"账号 {self.current_account.account_id} 执行失败: {e}")
                
                # 标记错误
                self.current_account.mark_error()
                
                # 如果是客户端错误（4xx），不进行重试
                if isinstance(e, APIRequestError) and hasattr(e, 'status_code'):
                    if 400 <= e.status_code < 500:
                        logger.info("客户端错误，不进行故障转移")
                        break
                
                # 继续尝试下一个账号
                continue
                
            except Exception as e:
                last_error = e
                logger.error(f"账号 {self.current_account.account_id} 执行异常: {e}")
                self.current_account.mark_error()
                continue
        
        # 所有账号都失败了
        error_msg = f"所有账号都不可用，最后错误: {last_error}"
        logger.error(error_msg)
        return APIResponse(ok=False, message=error_msg)
    
    # 实现所有抽象方法，使用故障转移机制
    def send_text(self, req) -> APIResponse:
        return self._execute_with_failover("send_text", req)
    
    def send_image(self, req) -> APIResponse:
        return self._execute_with_failover("send_image", req)
    
    def send_app(self, req) -> APIResponse:
        return self._execute_with_failover("send_app", req)
    
    def send_voice(self, req) -> APIResponse:
        return self._execute_with_failover("send_voice", req)
    
    def share_card(self, req) -> APIResponse:
        return self._execute_with_failover("share_card", req)
    
    def revoke_message(self, req) -> APIResponse:
        return self._execute_with_failover("revoke_message", req)
    
    def forward_mini_app(self, req) -> APIResponse:
        return self._execute_with_failover("forward_mini_app", req)
    
    def accept_friend(self, req) -> APIResponse:
        return self._execute_with_failover("accept_friend", req)
    
    def get_user_openid(self, req) -> APIResponse:
        return self._execute_with_failover("get_user_openid", req)
    
    def invite_group_member(self, req) -> APIResponse:
        return self._execute_with_failover("invite_group_member", req)
    
    def remove_group_member(self, req) -> APIResponse:
        return self._execute_with_failover("remove_group_member", req)
    
    def get_group_info(self, req) -> APIResponse:
        return self._execute_with_failover("get_group_info", req)
    
    def get_group_member_detail(self, req) -> APIResponse:
        return self._execute_with_failover("get_group_member_detail", req)
    
    def confirm_collection(self, req) -> APIResponse:
        return self._execute_with_failover("confirm_collection", req)
    
    def get_online_status(self, req) -> APIResponse:
        return self._execute_with_failover("get_online_status", req)
    
    def upload_image_to_cdn(self, req) -> APIResponse:
        return self._execute_with_failover("upload_image_to_cdn", req)
    
    # 管理方法
    def add_account(self, account: WechatAccount):
        """添加账号"""
        self.accounts.append(account)
        self.accounts.sort(key=lambda x: (x.priority, -x.get_success_rate()))
        logger.info(f"添加账号: {account.account_id}")
    
    def remove_account(self, account_id: str):
        """移除账号"""
        self.accounts = [acc for acc in self.accounts if acc.account_id != account_id]
        if account_id in self.adapters:
            del self.adapters[account_id]
        if self.current_account and self.current_account.account_id == account_id:
            self.current_account = None
        logger.info(f"移除账号: {account_id}")
    
    def get_account_stats(self) -> dict:
        """获取账号统计信息"""
        stats = {
            "total_accounts": len(self.accounts),
            "available_accounts": len(self._get_available_accounts()),
            "current_account": self.current_account.account_id if self.current_account else None,
            "accounts": []
        }
        
        for acc in self.accounts:
            stats["accounts"].append({
                "account_id": acc.account_id,
                "api_version": acc.api_version,
                "status": acc.status,
                "priority": acc.priority,
                "success_rate": acc.get_success_rate(),
                "total_requests": acc.success_count + acc.error_count,
                "last_used": acc.last_used_time
            })
        
        return stats
    
    def force_switch_account(self, account_id: str = None):
        """强制切换到指定账号"""
        if account_id:
            target_account = next((acc for acc in self.accounts if acc.account_id == account_id), None)
            if target_account and target_account.is_available():
                self.current_account = target_account
                self.last_switch_time = time.time()
                logger.info(f"强制切换到账号: {account_id}")
            else:
                logger.warning(f"账号 {account_id} 不存在或不可用")
        else:
            self.current_account = None
            logger.info("重置当前账号，下次调用时重新选择")
