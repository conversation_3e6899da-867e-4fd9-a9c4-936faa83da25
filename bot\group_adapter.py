"""
组适配器
管理单个组的多账号微信API适配器，实现故障转移
"""
import time
from typing import List, Optional, Dict, Any
from loguru import logger

from send_adapter.account import WechatAccount, AccountStatus
from send_adapter.factory import create_multi_account_adapter
from send_adapter.multi_account_adapter import MultiAccountAdapter
from send_adapter.models import APIResponse
from database import AgentConfig, DatabaseManager


class GroupAdapter:
    """组适配器，管理单个组的多账号故障转移"""
    
    def __init__(self, group_name: str, db_manager: DatabaseManager):
        """
        初始化组适配器
        
        Args:
            group_name: 组名
            db_manager: 数据库管理器
        """
        self.group_name = group_name
        self.db_manager = db_manager
        self.accounts: List[WechatAccount] = []
        self.multi_adapter: Optional[MultiAccountAdapter] = None
        self.last_refresh_time = 0
        self.refresh_interval = 300  # 5分钟刷新一次配置
        
        # 初始化加载配置
        self._load_accounts()
        
        logger.info(f"组适配器 {group_name} 初始化完成，共 {len(self.accounts)} 个账号")
    
    def _load_accounts(self):
        """从数据库加载账号配置"""
        try:
            # 获取组内所有代理商配置
            agent_configs = self.db_manager.get_agents_by_group(self.group_name)
            
            if not agent_configs:
                logger.warning(f"组 {self.group_name} 没有找到可用的代理商配置")
                return
            
            # 转换为账号对象
            accounts = []
            for config in agent_configs:
                account = self._create_account_from_config(config)
                if account:
                    accounts.append(account)
            
            self.accounts = accounts
            
            # 创建多账号适配器
            if self.accounts:
                self.multi_adapter = create_multi_account_adapter(self.accounts)
                logger.info(f"组 {self.group_name} 创建多账号适配器成功，可用账号: {len(self.accounts)}")
            else:
                logger.warning(f"组 {self.group_name} 没有可用的账号")
            
            self.last_refresh_time = time.time()
            
        except Exception as e:
            logger.error(f"加载组 {self.group_name} 账号配置失败: {e}")
    
    def _create_account_from_config(self, config: AgentConfig) -> Optional[WechatAccount]:
        """从代理商配置创建账号对象"""
        try:
            # 确定账号状态
            if not config.wechat_is_active:
                status = AccountStatus.DISABLED
            elif config.wechat_login_status == 0:
                status = AccountStatus.INACTIVE
            else:
                status = AccountStatus.ACTIVE
            
            # 根据API版本创建账号
            if config.api_version == "v1":
                if not config.token or not config.wechat_base_url:
                    logger.warning(f"V1账号 {config.name} 缺少必要配置")
                    return None
                
                account = WechatAccount.create_v1_account(
                    account_id=f"{self.group_name}_v1_{config.id}",
                    account_name=f"{config.name} (V1)",
                    wxid=config.wxid or f"wxid_{config.id}",
                    base_url=config.wechat_base_url,
                    api_key=config.token,
                    priority=config.id  # ID越小优先级越高
                )
                
            elif config.api_version == "v2":
                if not config.wxid or not config.wechat_base_url:
                    logger.warning(f"V2账号 {config.name} 缺少必要配置")
                    return None
                
                # V2使用wxid作为认证，不需要token
                account = WechatAccount.create_v2_account(
                    account_id=f"{self.group_name}_v2_{config.id}",
                    account_name=f"{config.name} (V2)",
                    wxid=config.wxid,
                    base_url=config.wechat_base_url,
                    api_key=config.wxid,  # V2使用wxid作为key
                    priority=config.id
                )
                
            elif config.api_version == "v3":
                if not config.token or not config.app_id or not config.wechat_base_url:
                    logger.warning(f"V3账号 {config.name} 缺少必要配置")
                    return None
                
                account = WechatAccount.create_v3_account(
                    account_id=f"{self.group_name}_v3_{config.id}",
                    account_name=f"{config.name} (V3)",
                    wxid=config.wxid or f"wxid_{config.id}",
                    base_url=config.wechat_base_url,
                    gewe_token=config.token,
                    gewe_appid=config.app_id,
                    priority=config.id
                )
                
            else:
                logger.warning(f"不支持的API版本: {config.api_version}")
                return None
            
            # 设置账号状态
            account.status = status
            
            # 添加原始配置信息到扩展配置
            account.extra_config.update({
                'agent_id': config.id,
                'agent_name': config.name,
                'contact': config.contact,
                'last_check_time': config.last_check_time
            })
            
            logger.debug(f"创建账号: {account.account_name} (状态: {status})")
            return account
            
        except Exception as e:
            logger.error(f"创建账号失败 {config.name}: {e}")
            return None
    
    def _should_refresh(self) -> bool:
        """判断是否需要刷新配置"""
        return time.time() - self.last_refresh_time > self.refresh_interval
    
    def _update_account_status(self, account: WechatAccount, success: bool):
        """更新账号状态到数据库"""
        try:
            agent_id = account.extra_config.get('agent_id')
            if agent_id:
                # 更新数据库中的登录状态
                new_status = 1 if success else 0
                self.db_manager.update_login_status(agent_id, new_status)
                
                # 更新本地账号状态
                if success:
                    account.mark_success()
                    if account.status == AccountStatus.ERROR:
                        account.status = AccountStatus.ACTIVE
                else:
                    account.mark_error()
                    
        except Exception as e:
            logger.error(f"更新账号状态失败: {e}")
    
    def send_message(self, method_name: str, *args, **kwargs) -> APIResponse:
        """
        发送消息，支持故障转移
        
        Args:
            method_name: 方法名 (如 'send_text', 'send_image' 等)
            *args, **kwargs: 方法参数
            
        Returns:
            API响应
        """
        # 检查是否需要刷新配置
        if self._should_refresh():
            logger.info(f"刷新组 {self.group_name} 的账号配置")
            self._load_accounts()
        
        if not self.multi_adapter:
            return APIResponse(ok=False, message=f"组 {self.group_name} 没有可用的账号")
        
        try:
            # 执行方法
            response = getattr(self.multi_adapter, method_name)(*args, **kwargs)
            
            # 更新账号状态
            current_account = self.multi_adapter.current_account
            if current_account:
                self._update_account_status(current_account, response.ok)
            
            return response
            
        except Exception as e:
            logger.error(f"组 {self.group_name} 发送消息失败: {e}")
            return APIResponse(ok=False, message=str(e))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取组统计信息"""
        if not self.multi_adapter:
            return {
                'group_name': self.group_name,
                'total_accounts': 0,
                'available_accounts': 0,
                'current_account': None,
                'accounts': []
            }
        
        stats = self.multi_adapter.get_account_stats()
        stats['group_name'] = self.group_name
        
        # 添加数据库相关信息
        for account_info in stats.get('accounts', []):
            account_id = account_info.get('account_id', '')
            # 查找对应的账号对象
            for account in self.accounts:
                if account.account_id == account_id:
                    account_info.update({
                        'agent_id': account.extra_config.get('agent_id'),
                        'agent_name': account.extra_config.get('agent_name'),
                        'contact': account.extra_config.get('contact'),
                        'last_check_time': account.extra_config.get('last_check_time')
                    })
                    break
        
        return stats
    
    def force_refresh(self):
        """强制刷新账号配置"""
        logger.info(f"强制刷新组 {self.group_name} 的账号配置")
        self._load_accounts()
    
    def get_available_accounts_count(self) -> int:
        """获取可用账号数量"""
        if not self.multi_adapter:
            return 0
        return len(self.multi_adapter._get_available_accounts())
    
    def is_healthy(self) -> bool:
        """检查组是否健康（至少有一个可用账号）"""
        return self.get_available_accounts_count() > 0

    # 便捷方法，直接调用常用的发送方法
    def send_text(self, request) -> APIResponse:
        """发送文本消息"""
        return self.send_message('send_text', request)

    def send_image(self, request) -> APIResponse:
        """发送图片消息"""
        return self.send_message('send_image', request)

    def send_voice(self, request) -> APIResponse:
        """发送语音消息"""
        return self.send_message('send_voice', request)

    def send_app(self, request) -> APIResponse:
        """发送应用消息"""
        return self.send_message('send_app', request)

    def share_card(self, request) -> APIResponse:
        """分享名片"""
        return self.send_message('share_card', request)

    def forward_mini_app(self, request) -> APIResponse:
        """转发小程序"""
        return self.send_message('forward_mini_app', request)
