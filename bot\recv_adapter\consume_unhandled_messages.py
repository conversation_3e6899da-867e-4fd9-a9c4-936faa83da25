#!/usr/bin/env python3
"""
消费特定wxid的未处理消息示例
"""
import asyncio
import json
import sys
from typing import Optional
from loguru import logger
from utils.mq_publisher import UnifiedMQPublisher

class UnhandledMessageConsumer:
    """未处理消息消费者"""
    
    def __init__(self, wxid: str):
        self.wxid = wxid
        self.queue_name = f"unhandled.{wxid}"
        self.publisher = UnifiedMQPublisher()
        self.running = False
        
    async def start(self):
        """开始消费消息"""
        try:
            # 连接到RabbitMQ
            await self.publisher.connect()
            logger.info(f"Connected to RabbitMQ for consuming {self.queue_name}")

            # 检查队列是否存在
            try:
                queue = await self.publisher.channel.declare_queue(self.queue_name, passive=True)
                logger.info(f"Queue {self.queue_name} exists")

                # 获取队列信息
                queue_info = await self.publisher.channel.queue_declare(self.queue_name, passive=True)
                message_count = queue_info.method.message_count
                logger.info(f"Queue {self.queue_name} has {message_count} messages")

                if message_count == 0:
                    logger.info("No messages in queue, waiting for new messages...")

                # 开始消费
                self.running = True
                consumer_tag = await queue.consume(self._message_handler)
                logger.info(f"Started consuming from {self.queue_name}")

                # 保持运行直到停止
                while self.running:
                    await asyncio.sleep(1)

                # 停止消费
                await self.publisher.channel.basic_cancel(consumer_tag)
                logger.info("Stopped consuming")

            except Exception as e:
                logger.error(f"Queue {self.queue_name} does not exist or error: {e}")
                logger.info("Queue will be created automatically when messages are published")

        except Exception as e:
            logger.error(f"Failed to start consumer: {e}")
            raise
        finally:
            await self.publisher.disconnect()
            
    async def stop(self):
        """停止消费"""
        self.running = False
        logger.info("Stopping consumer...")
        
    async def _message_handler(self, message):
        """处理接收到的消息"""
        async with message.process():
            try:
                # 解析消息
                body = message.body.decode('utf-8')
                data = json.loads(body)
                
                # 提取关键信息
                msg_id = data.get('msg_id', 'unknown')
                content = data.get('content', '')
                from_user = data.get('from_user_name', '')
                timestamp = data.get('timestamp', 0)
                msg_type = data.get('msg_type', 0)
                
                logger.info(f"📨 Received unhandled message:")
                logger.info(f"   Message ID: {msg_id}")
                logger.info(f"   From: {from_user}")
                logger.info(f"   Type: {msg_type}")
                logger.info(f"   Content: {content}")
                logger.info(f"   Timestamp: {timestamp}")
                
                # 在这里添加你的处理逻辑
                await self._process_unhandled_message(data)
                
                logger.info(f"✅ Processed message {msg_id}")
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse message JSON: {e}")
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                
    async def _process_unhandled_message(self, message_data: dict):
        """处理未处理的消息 - 在这里实现你的业务逻辑"""
        
        # 示例处理逻辑
        msg_type = message_data.get('msg_type', 0)
        content = message_data.get('content', '')
        
        if msg_type == 1:  # 文本消息
            logger.info(f"Processing text message: {content}")
            # 在这里添加文本消息的处理逻辑
            
        elif msg_type == 3:  # 图片消息
            logger.info("Processing image message")
            # 在这里添加图片消息的处理逻辑
            
        elif msg_type == 47:  # 表情消息
            logger.info("Processing emoji message")
            # 在这里添加表情消息的处理逻辑
            
        else:
            logger.info(f"Processing message type {msg_type}")
            # 在这里添加其他类型消息的处理逻辑
        
        # 模拟处理时间
        await asyncio.sleep(0.1)

async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("Usage: python consume_unhandled_messages.py <wxid>")
        print("Example: python consume_unhandled_messages.py wxid_123456789")
        sys.exit(1)
    
    wxid = sys.argv[1]
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <5}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    logger.info(f"Starting unhandled message consumer for wxid: {wxid}")
    
    # 创建消费者
    consumer = UnhandledMessageConsumer(wxid)
    
    # 设置信号处理
    import signal
    
    def signal_handler():
        logger.info("Received shutdown signal")
        asyncio.create_task(consumer.stop())
    
    # 注册信号处理器
    for sig in [signal.SIGTERM, signal.SIGINT]:
        signal.signal(sig, lambda s, f: signal_handler())
    
    try:
        # 开始消费
        await consumer.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Consumer error: {e}")
        import traceback
        logger.debug(f"Full traceback: {traceback.format_exc()}")
    finally:
        logger.info("Consumer stopped")

if __name__ == "__main__":
    asyncio.run(main())
