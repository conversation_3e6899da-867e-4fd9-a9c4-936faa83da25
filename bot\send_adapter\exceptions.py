"""自定义异常定义"""
from typing import Optional


class APIAdapterError(Exception):
    """基础异常"""
    def __init__(self, message: str, *, status_code: Optional[int] = None):
        super().__init__(message)
        self.status_code = status_code


class APIRequestError(APIAdapterError):
    """HTTP 请求相关异常"""


class APIServerError(APIAdapterError):
    """服务端错误(5xx)"""


class APIValidationError(APIAdapterError):
    """入参或返回数据校验错误"""

