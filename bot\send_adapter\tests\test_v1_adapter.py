from unittest.mock import patch, MagicMock
from send_adapter.factory import create_adapter
from send_adapter.models import (
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
)


def test_v1_routes_and_payloads(monkeypatch):
    monkeypatch.setenv("API_VERSION", "v1")
    adapter = create_adapter("v1")

    with patch("send_adapter.v1_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok

        adapter.send_text(SendTextRequest(wxid="me", to_wxid="you", content="hi", at=["a"]))
        assert mock_post.call_args[0][0].endswith("/message/SendMessage")
        body = mock_post.call_args[1]["json"]["MsgItem"][0]
        assert body["MsgType"] == 1
        assert body["ToUserName"] == "you"

        adapter.send_image(SendImageRequest(wxid="me", to_wxid="you", image_base64="b64"))
        assert mock_post.call_args[1]["json"]["MsgItem"][0]["MsgType"] == 2

        adapter.send_app(SendAppRequest(wxid="me", to_wxid="you", xml="<xml/>", type=2001))
        assert mock_post.call_args[0][0].endswith("/message/SendAppMessage")

        adapter.send_voice(SendVoiceRequest(wxid="me", to_wxid="you", voice_base64="xx", codec_type=4, voice_time_ms=2500))
        assert mock_post.call_args[0][0].endswith("/message/SendUploadVoice")

        adapter.share_card(ShareCardRequest(wxid="me", to_wxid="you", card_wxid="c"))
        assert mock_post.call_args[0][0].endswith("/message/ShareCard")

        adapter.revoke_message(RevokeMessageRequest(wxid="me", to_user_name="u", client_msg_id=1, create_time=2, new_msg_id=3))
        assert mock_post.call_args[0][0].endswith("/message/RevokeMsg")

        adapter.upload_image_to_cdn(UploadImageToCDNRequest(wxid="me", base64="b"))
        assert mock_post.call_args[0][0].endswith("/sns/UploadFriendCircleImage")

        adapter.accept_friend(AcceptFriendRequest(wxid="me", v1="v1", v2="v2", scene=3))
        assert mock_post.call_args[0][0].endswith("/friend/PassVerify")

        adapter.get_user_openid(GetUserOpenidRequest(wxid="me", appid="app", to_wxid="u"))
        assert mock_post.call_args[0][0].endswith("/applet/GetUserOpenId")

        adapter.invite_group_member(InviteGroupMemberRequest(wxid="me", chatroom_name="r", to_wxids=["a"]))
        assert mock_post.call_args[0][0].endswith("/group/InviteChatroomMembers")

        adapter.remove_group_member(RemoveGroupMemberRequest(wxid="me", chatroom_name="r", to_wxids=["a"]))
        assert mock_post.call_args[0][0].endswith("/group/DelChatRoomMember")

        adapter.get_group_info(GetGroupInfoRequest(wxid="me", qid="q"))
        assert mock_post.call_args[0][0].endswith("/group/GetChatroomInfo")

        adapter.get_group_member_detail(GetGroupMembersRequest(wxid="me", qid="q"))
        assert mock_post.call_args[0][0].endswith("/group/GetChatroomMemberDetail")

        adapter.confirm_collection(ConfirmCollectionRequest(wxid="me", to_user_name="u", transfer_id="t", transaction_id="tx"))
        assert mock_post.call_args[0][0].endswith("/pay/Collectmoney")

        adapter.get_online_status(GetOnlineStatusRequest(wxid="me"))
        assert mock_post.call_args[0][0].endswith("/login/GetCacheInfo")

