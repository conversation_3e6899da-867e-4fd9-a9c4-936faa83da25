#!/usr/bin/env python3
"""
基于账号对象的适配器使用示例
演示新的账号管理和多账号故障转移功能
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.account import WechatAccount, AccountManager, APIVersion, AccountStatus
from send_adapter.factory import create_adapter, create_multi_account_adapter
from send_adapter.models import SendTextRequest


def create_sample_accounts():
    """创建示例账号"""
    accounts = []
    
    # V1 账号
    v1_account = WechatAccount.create_v1_account(
        account_id="v1_primary",
        account_name="V1主账号",
        wxid="wxid_v1_primary",
        base_url="http://8.133.252.208:8080",
        api_key="your_v1_key",
        priority=1
    )
    accounts.append(v1_account)
    
    # V2 账号
    v2_account = WechatAccount.create_v2_account(
        account_id="v2_backup",
        account_name="V2备用账号",
        wxid="wxid_v2_backup",
        base_url="http://8.133.252.208:8060/api",
        api_key="your_v2_key",
        priority=2
    )
    accounts.append(v2_account)
    
    # V3 账号
    v3_account = WechatAccount.create_v3_account(
        account_id="v3_main",
        account_name="V3主账号",
        wxid="wxid_yba********",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl",
        priority=1
    )
    accounts.append(v3_account)
    
    return accounts


def demo_single_account():
    """演示单账号使用"""
    print("🔧 单账号适配器演示")
    print("-" * 40)
    
    # 创建V3账号
    account = WechatAccount.create_v3_account(
        account_id="demo_v3",
        account_name="演示V3账号",
        wxid="wxid_yba********",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"
    )
    
    print(f"📱 账号信息: {account}")
    print(f"🔗 API版本: {account.api_version}")
    print(f"🌐 基础URL: {account.base_url}")
    
    # 创建适配器
    adapter = create_adapter(account)
    
    # 发送消息
    request = SendTextRequest(
        wxid=account.wxid,
        to_wxid="YBA-********",
        content="Hello from account-based adapter! 🚀"
    )
    
    try:
        response = adapter.send_text(request)
        if response.ok:
            print("✅ 消息发送成功!")
            account.mark_success()
        else:
            print(f"❌ 发送失败: {response.message}")
            account.mark_error()
    except Exception as e:
        print(f"❌ 异常: {e}")
        account.mark_error()
    
    print(f"📊 账号统计: 成功率 {account.get_success_rate():.2%}")
    return account


def demo_multi_account():
    """演示多账号故障转移"""
    print("\n🔄 多账号故障转移演示")
    print("-" * 40)
    
    # 创建多个账号
    accounts = create_sample_accounts()
    
    # 模拟一些账号不可用
    accounts[0].status = AccountStatus.ERROR  # V1账号错误
    accounts[1].status = AccountStatus.INACTIVE  # V2账号不活跃
    # V3账号保持可用
    
    print("📋 账号列表:")
    for acc in accounts:
        print(f"  - {acc.account_id}: {acc.api_version} ({acc.status})")
    
    # 创建多账号适配器
    multi_adapter = create_multi_account_adapter(accounts)
    
    print(f"\n🎯 可用账号: {len(multi_adapter._get_available_accounts())} 个")
    
    # 发送消息（会自动选择可用账号）
    request = SendTextRequest(
        wxid="auto_select",  # 会被实际选中的账号覆盖
        to_wxid="YBA-********",
        content="Hello from multi-account adapter with failover! 🔄"
    )
    
    try:
        response = multi_adapter.send_text(request)
        if response.ok:
            print("✅ 多账号消息发送成功!")
        else:
            print(f"❌ 多账号发送失败: {response.message}")
    except Exception as e:
        print(f"❌ 多账号异常: {e}")
    
    # 显示统计信息
    stats = multi_adapter.get_account_stats()
    print(f"\n📊 多账号统计:")
    print(f"  - 总账号数: {stats['total_accounts']}")
    print(f"  - 可用账号数: {stats['available_accounts']}")
    print(f"  - 当前使用: {stats['current_account']}")
    
    return multi_adapter


def demo_account_manager():
    """演示账号管理器"""
    print("\n📚 账号管理器演示")
    print("-" * 40)
    
    # 创建账号管理器
    manager = AccountManager()
    
    # 添加账号
    accounts = create_sample_accounts()
    for acc in accounts:
        manager.add_account(acc)
    
    print(f"📝 已添加 {len(accounts)} 个账号")
    
    # 获取主要账号
    primary = manager.get_primary_account()
    if primary:
        print(f"🎯 主要账号: {primary.account_id} ({primary.api_version})")
    
    # 获取备用账号
    backups = manager.get_backup_accounts()
    print(f"🔄 备用账号: {len(backups)} 个")
    for backup in backups:
        print(f"  - {backup.account_id} ({backup.api_version})")
    
    # 按版本获取账号
    v3_accounts = manager.get_accounts_by_version(APIVersion.V3)
    print(f"🔧 V3账号: {len(v3_accounts)} 个")
    
    # 获取统计信息
    stats = manager.get_stats()
    print(f"\n📊 管理器统计:")
    print(f"  - 总账号: {stats['total_accounts']}")
    print(f"  - 可用账号: {stats['available_accounts']}")
    print(f"  - 版本分布: {stats['version_distribution']}")
    
    return manager


def demo_account_failover():
    """演示账号故障转移场景"""
    print("\n⚠️ 故障转移场景演示")
    print("-" * 40)
    
    # 创建账号，模拟不同的故障情况
    accounts = []
    
    # 主账号 - 模拟高错误率
    main_account = WechatAccount.create_v3_account(
        account_id="main_failing",
        account_name="主账号(故障中)",
        wxid="wxid_main",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="invalid_token",  # 故意使用无效token
        gewe_appid="invalid_appid",
        priority=1
    )
    # 模拟多次错误
    for _ in range(6):
        main_account.mark_error()
    accounts.append(main_account)
    
    # 备用账号 - 正常
    backup_account = WechatAccount.create_v3_account(
        account_id="backup_working",
        account_name="备用账号(正常)",
        wxid="wxid_yba********",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl",
        priority=2
    )
    accounts.append(backup_account)
    
    print("📋 故障场景账号:")
    for acc in accounts:
        print(f"  - {acc.account_id}: {acc.status} (成功率: {acc.get_success_rate():.2%})")
    
    # 创建多账号适配器
    failover_adapter = create_multi_account_adapter(accounts)
    
    # 尝试发送消息
    request = SendTextRequest(
        wxid="auto_select",
        to_wxid="YBA-********",
        content="Testing failover mechanism! 🛡️"
    )
    
    print("\n🔄 执行故障转移测试...")
    try:
        response = failover_adapter.send_text(request)
        if response.ok:
            print("✅ 故障转移成功，消息已发送!")
            stats = failover_adapter.get_account_stats()
            print(f"🎯 使用账号: {stats['current_account']}")
        else:
            print(f"❌ 故障转移失败: {response.message}")
    except Exception as e:
        print(f"❌ 故障转移异常: {e}")
    
    return failover_adapter


def main():
    """主函数"""
    print("🚀 基于账号对象的适配器演示")
    print("=" * 50)
    
    # 1. 单账号演示
    single_account = demo_single_account()
    
    # 2. 多账号演示
    multi_adapter = demo_multi_account()
    
    # 3. 账号管理器演示
    manager = demo_account_manager()
    
    # 4. 故障转移演示
    failover_adapter = demo_account_failover()
    
    print("\n" + "=" * 50)
    print("🎉 演示完成!")
    print("\n💡 新架构优势:")
    print("1. 🏗️ 统一的账号对象管理")
    print("2. 🔄 自动故障转移机制")
    print("3. 📊 详细的统计和监控")
    print("4. 🎯 智能账号选择策略")
    print("5. 🔧 灵活的配置管理")
    print("6. 📈 为多账号扩展做好准备")
    
    print("\n🔧 使用建议:")
    print("- 生产环境建议配置多个不同版本的账号")
    print("- 设置合理的优先级和重试策略")
    print("- 定期监控账号状态和成功率")
    print("- 根据业务需求调整故障转移策略")


if __name__ == "__main__":
    main()
