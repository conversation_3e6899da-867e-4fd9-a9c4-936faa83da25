#!/usr/bin/env python3
"""
V3 接口快速发送消息示例 - 简化版本
直接在创建适配器时传入配置，无需环境变量
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import SendTextRequest


def quick_send_to_yba():
    """快速发送消息给 YBA-19990312"""

    # 直接在创建适配器时传入配置，无需环境变量
    adapter = create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",    # 🔑 替换为你的 GeWe Token
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"         # 🔑 替换为你的 App ID
    )

    request = SendTextRequest(
        wxid="your_bot_wxid",        # 🤖 替换为你的机器人微信ID
        to_wxid="YBA-19990312",      # 🎯 目标用户
        content="Hi YBA! 这是通过 V3 接口发送的消息 👋"
    )

    try:
        response = adapter.send_text(request)
        if response.ok:
            print("✅ 消息发送成功!")
            print(f"📋 响应: {response.data}")
        else:
            print(f"❌ 发送失败: {response.message}")
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    quick_send_to_yba()
