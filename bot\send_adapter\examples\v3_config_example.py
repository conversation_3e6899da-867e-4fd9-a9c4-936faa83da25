#!/usr/bin/env python3
"""
V3 接口配置示例 - 演示不同的配置方式
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import SendTextRequest


def example_1_direct_config():
    """示例1: 直接在创建适配器时传入配置"""
    print("📝 示例1: 直接配置方式")
    
    adapter = create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="your_actual_token",
        gewe_appid="your_actual_appid"
    )
    
    request = SendTextRequest(
        wxid="bot_wxid",
        to_wxid="YBA-19990312",
        content="Hello from direct config!"
    )
    
    try:
        response = adapter.send_text(request)
        print(f"✅ 发送结果: {response.ok}")
    except Exception as e:
        print(f"❌ 错误: {e}")


def example_2_config_dict():
    """示例2: 使用配置字典"""
    print("\n📝 示例2: 配置字典方式")
    
    v3_config = {
        "v3_base_url": "http://api.geweapi.com/gewe/v2/api",
        "gewe_token": "your_actual_token",
        "gewe_appid": "your_actual_appid",
        "request_timeout": 30,
        "max_retries": 3
    }
    
    adapter = create_adapter("v3", **v3_config)
    
    request = SendTextRequest(
        wxid="bot_wxid",
        to_wxid="YBA-19990312", 
        content="Hello from config dict!"
    )
    
    try:
        response = adapter.send_text(request)
        print(f"✅ 发送结果: {response.ok}")
    except Exception as e:
        print(f"❌ 错误: {e}")


def example_3_class_based():
    """示例3: 基于类的配置管理"""
    print("\n📝 示例3: 类配置方式")
    
    class V3Config:
        V3_BASE_URL = "http://api.geweapi.com/gewe/v2/api"
        GEWE_TOKEN = "your_actual_token"
        GEWE_APPID = "your_actual_appid"
        REQUEST_TIMEOUT = 30
        MAX_RETRIES = 3
        
        @classmethod
        def to_dict(cls):
            return {
                "v3_base_url": cls.V3_BASE_URL,
                "gewe_token": cls.GEWE_TOKEN,
                "gewe_appid": cls.GEWE_APPID,
                "request_timeout": cls.REQUEST_TIMEOUT,
                "max_retries": cls.MAX_RETRIES
            }
    
    adapter = create_adapter("v3", **V3Config.to_dict())
    
    request = SendTextRequest(
        wxid="bot_wxid",
        to_wxid="YBA-19990312",
        content="Hello from class config!"
    )
    
    try:
        response = adapter.send_text(request)
        print(f"✅ 发送结果: {response.ok}")
    except Exception as e:
        print(f"❌ 错误: {e}")


def example_4_multiple_versions():
    """示例4: 同时使用多个版本的适配器"""
    print("\n📝 示例4: 多版本适配器")
    
    # V1 配置
    v1_adapter = create_adapter("v1",
        v1_base_url="http://*************:8080",
        v1_key="your_v1_key"
    )
    
    # V2 配置
    v2_adapter = create_adapter("v2",
        v2_base_url="http://*************:8060/api",
        v2_key="your_v2_key"
    )
    
    # V3 配置
    v3_adapter = create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="your_gewe_token",
        gewe_appid="your_gewe_appid"
    )
    
    # 相同的请求模型，不同的适配器
    request = SendTextRequest(
        wxid="bot_wxid",
        to_wxid="YBA-19990312",
        content="Hello from multi-version!"
    )
    
    print("🔄 使用不同版本发送相同消息:")
    
    for version, adapter in [("V1", v1_adapter), ("V2", v2_adapter), ("V3", v3_adapter)]:
        try:
            response = adapter.send_text(request)
            print(f"   {version}: {'✅' if response.ok else '❌'}")
        except Exception as e:
            print(f"   {version}: ❌ {e}")


def main():
    """主函数"""
    print("🚀 V3 接口配置示例")
    print("=" * 50)
    
    # 运行所有示例
    example_1_direct_config()
    example_2_config_dict()
    example_3_class_based()
    example_4_multiple_versions()
    
    print("\n" + "=" * 50)
    print("💡 配置参数说明:")
    print("- v3_base_url: V3 API 基础URL")
    print("- gewe_token: GeWe 平台认证Token")
    print("- gewe_appid: GeWe 平台应用ID")
    print("- request_timeout: 请求超时时间（秒）")
    print("- max_retries: 最大重试次数")
    print("- backoff_factor: 重试退避因子")


if __name__ == "__main__":
    main()
