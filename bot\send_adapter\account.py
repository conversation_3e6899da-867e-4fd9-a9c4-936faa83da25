"""
账号管理模块
定义账号对象，包含API版本、配置信息等
"""
from typing import Literal, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class APIVersion(str, Enum):
    """API版本枚举"""
    V1 = "v1"
    V2 = "v2"
    V3 = "v3"


class AccountStatus(str, Enum):
    """账号状态枚举"""
    ACTIVE = "active"           # 活跃可用
    INACTIVE = "inactive"       # 暂时不可用
    DISABLED = "disabled"       # 已禁用
    ERROR = "error"            # 错误状态


class WechatAccount(BaseModel):
    """微信账号对象"""
    
    # 基础信息
    account_id: str = Field(..., description="账号唯一标识")
    account_name: str = Field(..., description="账号名称/描述")
    wxid: str = Field(..., description="微信ID")
    
    # API配置
    api_version: APIVersion = Field(..., description="API版本")
    priority: int = Field(default=1, description="优先级，数字越小优先级越高")
    status: AccountStatus = Field(default=AccountStatus.ACTIVE, description="账号状态")
    
    # 连接配置
    base_url: str = Field(..., description="API基础URL")
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    backoff_factor: float = Field(default=1.0, description="重试退避因子")
    
    # 认证信息（根据版本不同而不同）
    auth_config: Dict[str, Any] = Field(default_factory=dict, description="认证配置")
    
    # 扩展配置
    extra_config: Dict[str, Any] = Field(default_factory=dict, description="扩展配置")
    
    # 统计信息
    success_count: int = Field(default=0, description="成功请求次数")
    error_count: int = Field(default=0, description="错误请求次数")
    last_used_time: Optional[float] = Field(default=None, description="最后使用时间戳")
    last_error_time: Optional[float] = Field(default=None, description="最后错误时间戳")
    
    class Config:
        use_enum_values = True
    
    @classmethod
    def create_v1_account(
        cls,
        account_id: str,
        account_name: str,
        wxid: str,
        base_url: str,
        api_key: str,
        priority: int = 1,
        **kwargs
    ) -> "WechatAccount":
        """创建V1账号"""
        return cls(
            account_id=account_id,
            account_name=account_name,
            wxid=wxid,
            api_version=APIVersion.V1,
            priority=priority,
            base_url=base_url,
            auth_config={"api_key": api_key},
            **kwargs
        )
    
    @classmethod
    def create_v2_account(
        cls,
        account_id: str,
        account_name: str,
        wxid: str,
        base_url: str,
        api_key: str,
        priority: int = 1,
        **kwargs
    ) -> "WechatAccount":
        """创建V2账号"""
        return cls(
            account_id=account_id,
            account_name=account_name,
            wxid=wxid,
            api_version=APIVersion.V2,
            priority=priority,
            base_url=base_url,
            auth_config={"api_key": api_key},
            **kwargs
        )
    
    @classmethod
    def create_v3_account(
        cls,
        account_id: str,
        account_name: str,
        wxid: str,
        base_url: str,
        gewe_token: str,
        gewe_appid: str,
        priority: int = 1,
        **kwargs
    ) -> "WechatAccount":
        """创建V3账号"""
        return cls(
            account_id=account_id,
            account_name=account_name,
            wxid=wxid,
            api_version=APIVersion.V3,
            priority=priority,
            base_url=base_url,
            auth_config={
                "gewe_token": gewe_token,
                "gewe_appid": gewe_appid
            },
            **kwargs
        )
    
    def get_api_key(self) -> Optional[str]:
        """获取API密钥（V1/V2）"""
        return self.auth_config.get("api_key")
    
    def get_gewe_token(self) -> Optional[str]:
        """获取GeWe Token（V3）"""
        return self.auth_config.get("gewe_token")
    
    def get_gewe_appid(self) -> Optional[str]:
        """获取GeWe App ID（V3）"""
        return self.auth_config.get("gewe_appid")
    
    def is_available(self) -> bool:
        """检查账号是否可用"""
        return self.status == AccountStatus.ACTIVE
    
    def mark_success(self, timestamp: float = None):
        """标记成功使用"""
        import time
        self.success_count += 1
        self.last_used_time = timestamp or time.time()
        # 如果之前是错误状态，恢复为活跃状态
        if self.status == AccountStatus.ERROR:
            self.status = AccountStatus.ACTIVE
    
    def mark_error(self, timestamp: float = None):
        """标记错误使用"""
        import time
        self.error_count += 1
        self.last_error_time = timestamp or time.time()
        # 连续错误过多时标记为错误状态
        if self.error_count > 5:
            self.status = AccountStatus.ERROR
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total = self.success_count + self.error_count
        if total == 0:
            return 1.0
        return self.success_count / total
    
    def to_adapter_config(self) -> Dict[str, Any]:
        """转换为适配器配置"""
        config = {
            "request_timeout": self.timeout,
            "max_retries": self.max_retries,
            "backoff_factor": self.backoff_factor,
        }

        # 处理字符串或枚举类型的api_version
        version = self.api_version if isinstance(self.api_version, str) else self.api_version.value

        if version == "v1":
            config.update({
                "v1_base_url": self.base_url,
                "v1_key": self.get_api_key(),
            })
        elif version == "v2":
            config.update({
                "v2_base_url": self.base_url,
                "v2_key": self.get_api_key(),
            })
        elif version == "v3":
            config.update({
                "v3_base_url": self.base_url,
                "gewe_token": self.get_gewe_token(),
                "gewe_appid": self.get_gewe_appid(),
            })

        # 添加扩展配置
        config.update(self.extra_config)

        return config
    
    def __str__(self) -> str:
        return f"WechatAccount({self.account_id}, {self.api_version}, {self.status})"
    
    def __repr__(self) -> str:
        return (f"WechatAccount(account_id='{self.account_id}', "
                f"api_version='{self.api_version}', status='{self.status}', "
                f"priority={self.priority})")


class AccountManager:
    """账号管理器"""
    
    def __init__(self):
        self.accounts: Dict[str, WechatAccount] = {}
    
    def add_account(self, account: WechatAccount):
        """添加账号"""
        self.accounts[account.account_id] = account
    
    def remove_account(self, account_id: str):
        """移除账号"""
        self.accounts.pop(account_id, None)
    
    def get_account(self, account_id: str) -> Optional[WechatAccount]:
        """获取指定账号"""
        return self.accounts.get(account_id)
    
    def get_available_accounts(self) -> list[WechatAccount]:
        """获取所有可用账号，按优先级排序"""
        available = [acc for acc in self.accounts.values() if acc.is_available()]
        return sorted(available, key=lambda x: (x.priority, -x.get_success_rate()))
    
    def get_accounts_by_version(self, version: APIVersion) -> list[WechatAccount]:
        """获取指定版本的可用账号"""
        version_str = version if isinstance(version, str) else version.value
        return [acc for acc in self.get_available_accounts()
                if (acc.api_version if isinstance(acc.api_version, str) else acc.api_version.value) == version_str]
    
    def get_primary_account(self) -> Optional[WechatAccount]:
        """获取主要账号（优先级最高的可用账号）"""
        available = self.get_available_accounts()
        return available[0] if available else None
    
    def get_backup_accounts(self, exclude_id: str = None) -> list[WechatAccount]:
        """获取备用账号列表"""
        available = self.get_available_accounts()
        if exclude_id:
            available = [acc for acc in available if acc.account_id != exclude_id]
        return available[1:] if len(available) > 1 else []
    
    def mark_account_error(self, account_id: str):
        """标记账号错误"""
        account = self.get_account(account_id)
        if account:
            account.mark_error()
    
    def mark_account_success(self, account_id: str):
        """标记账号成功"""
        account = self.get_account(account_id)
        if account:
            account.mark_success()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = len(self.accounts)
        available = len(self.get_available_accounts())
        
        version_stats = {}
        for version in APIVersion:
            version_accounts = self.get_accounts_by_version(version)
            version_stats[version.value] = len(version_accounts)
        
        return {
            "total_accounts": total,
            "available_accounts": available,
            "version_distribution": version_stats,
            "accounts": {
                acc_id: {
                    "status": acc.status,
                    "success_rate": acc.get_success_rate(),
                    "total_requests": acc.success_count + acc.error_count
                }
                for acc_id, acc in self.accounts.items()
            }
        }


# 全局账号管理器实例
account_manager = AccountManager()
