from unittest.mock import patch, MagicMock
import pytest

from send_adapter.factory import create_adapter
from send_adapter.models import (
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    GetOnlineStatusRequest,
)


def test_v3_headers_and_paths(monkeypatch):
    monkeypatch.setenv("API_VERSION", "v3")
    monkeypatch.setenv("GEWE_TOKEN", "token123")
    monkeypatch.setenv("GEWE_APPID", "app123")

    # 重新加载配置
    from importlib import reload
    from send_adapter import config as cfg
    reload(cfg)

    adapter = create_adapter("v3")

    with patch("send_adapter.v3_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok

        adapter.send_text(SendTextRequest(wxid="me", to_wxid="you", content="hi", at=["a"]))
        assert mock_post.call_args[0][0].endswith("/message/postText")
        payload = mock_post.call_args[1]["json"]
        assert payload["appId"] == "app123"
        headers = mock_post.call_args[1]["headers"]
        assert headers["X-GEWE-TOKEN"] == "token123"

        adapter.send_image(SendImageRequest(wxid="me", to_wxid="you", image_base64="b64"))
        assert mock_post.call_args[0][0].endswith("/message/postImage")

        adapter.send_app(SendAppRequest(wxid="me", to_wxid="you", xml="<xml/>", type=5))
        assert mock_post.call_args[0][0].endswith("/message/postAppMsg")

        adapter.send_voice(SendVoiceRequest(wxid="me", to_wxid="you", voice_base64="xx", codec_type=4, voice_time_ms=2000))
        assert mock_post.call_args[0][0].endswith("/message/postVoice")

        adapter.share_card(ShareCardRequest(wxid="me", to_wxid="you", card_wxid="c", card_nickname="nn"))
        assert mock_post.call_args[0][0].endswith("/message/postNameCard")

        adapter.revoke_message(RevokeMessageRequest(wxid="me", to_user_name="u", client_msg_id=1, create_time=2, new_msg_id=3))
        assert mock_post.call_args[0][0].endswith("/message/revokeMsg")

        adapter.upload_image_to_cdn(UploadImageToCDNRequest(wxid="me", base64="b"))
        assert mock_post.call_args[0][0].endswith("/sns/uploadSnsImage")

        adapter.invite_group_member(InviteGroupMemberRequest(wxid="me", chatroom_name="room", to_wxids=["a","b"]))
        assert mock_post.call_args[0][0].endswith("/group/inviteMember")

        adapter.remove_group_member(RemoveGroupMemberRequest(wxid="me", chatroom_name="room", to_wxids=["a"]))
        assert mock_post.call_args[0][0].endswith("/group/removeMember")

        adapter.get_group_info(GetGroupInfoRequest(wxid="me", qid="123"))
        assert mock_post.call_args[0][0].endswith("/group/getChatroomInfo")

        adapter.get_group_member_detail(GetGroupMembersRequest(wxid="me", qid="123"))
        assert mock_post.call_args[0][0].endswith("/group/getChatroomMemberDetail")

        adapter.get_online_status(GetOnlineStatusRequest(wxid="me"))
        assert mock_post.call_args[0][0].endswith("/login/checkOnline")

