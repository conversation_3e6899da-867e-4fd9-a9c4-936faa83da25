"""
统一MQ发布器 - 支持商品匹配结果和未处理消息发布
"""
import json
import aio_pika
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from loguru import logger

from config import settings


class UnifiedMQPublisher:
    """统一MQ发布器 - 支持多种消息类型发布"""

    def __init__(self):
        self.connection = None
        self.channel = None
        self.product_exchange = None

        # 预定义队列参数，避免重复创建
        self.unhandled_queue_args = {
            "x-message-ttl": 60000,      # 消息TTL 1分钟
            "x-expires": 3600000,        # 队列TTL 1小时
            "x-max-length": 3000,        # 队列最大长度，防止积压
            "x-overflow": "drop-head",   # 超限时丢弃最老的消息
        }

        # 缓存已声明的队列，避免重复声明
        self.declared_queues = set()

        # 预定义消息属性，避免重复创建
        self.message_properties = {
            "content_type": "application/json",
            "delivery_mode": aio_pika.DeliveryMode.PERSISTENT
        }

    async def connect(self):
        """连接到RabbitMQ"""
        try:
            connection_url = f"amqp://{settings.rabbitmq_username}:{settings.rabbitmq_password}@{settings.rabbitmq_host}:{settings.rabbitmq_port}{settings.rabbitmq_vhost}"
            self.connection = await aio_pika.connect_robust(connection_url)
            self.channel = await self.connection.channel()

            # 声明商品匹配交换机
            self.product_exchange = await self.channel.declare_exchange(
                "events.product-matches",
                aio_pika.ExchangeType.TOPIC,
                durable=True
            )

            logger.info("Unified MQ publisher connected to RabbitMQ")

        except Exception as e:
            logger.error(f"Failed to connect unified MQ publisher: {e}")
            raise
    
    async def disconnect(self):
        """断开连接"""
        if self.connection:
            await self.connection.close()
            logger.info("Unified MQ publisher disconnected")
    
    async def publish_product_matches(
        self,
        ts: str,
        msg_id: int,
        store: Dict[str, Any],
        products: List[Dict[str, Any]]
    ) -> bool:
        """
        发布商品匹配结果到MQ
        
        Args:
            ts: 时间戳
            msg_id: 消息ID
            store: 店铺信息 {"id": int, "name": str}
            products: 商品信息列表 [{"id": int, "name": str}, ...]
            
        Returns:
            bool: 是否发布成功
        """
        if not self.product_exchange:
            logger.error("Publisher not connected")
            return False

        if not products:
            logger.debug("No products to publish")
            return True

        try:
            # 构建事件载荷
            payload = {
                "ts": ts,
                "msg_id": msg_id,
                "store": store,
                "products": products
            }

            # 创建消息
            message = aio_pika.Message(
                body=json.dumps(payload, ensure_ascii=False).encode("utf-8"),
                content_type="application/json",
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                headers={
                    "event": "product.matches.v1",
                    "store_id": str(store["id"])
                }
            )

            # 发布消息
            routing_key = f"product.matches.store.{store['id']}"
            await self.product_exchange.publish(message, routing_key=routing_key)

            logger.info(f"Published product matches: store_id={store['id']}, products_count={len(products)}")
            return True

        except Exception as e:
            logger.error(f"Failed to publish product matches: {e}")
            return False

    async def publish_unhandled_message(
        self,
        message_data: Dict[str, Any],
        wxid: str
    ) -> bool:
        """
        发布未处理消息到对应wxid的队列

        Args:
            message_data: 消息数据
            wxid: 微信ID

        Returns:
            bool: 是否发布成功
        """
        if not self.channel:
            logger.error("Publisher not connected")
            return False

        try:
            # 构建队列名称
            queue_name = f"unhandled.{wxid}"

            # 优化：只在首次声明队列，避免重复声明开销
            if queue_name not in self.declared_queues:
                try:
                    await self.channel.declare_queue(
                        queue_name,
                        durable=True,
                        arguments=self.unhandled_queue_args,
                        passive=False
                    )
                    self.declared_queues.add(queue_name)
                except Exception:
                    # 如果队列已存在但参数不同，使用被动模式
                    await self.channel.declare_queue(queue_name, passive=True)
                    self.declared_queues.add(queue_name)

            # 创建消息 - 优化序列化和属性创建
            message_body = json.dumps(message_data, ensure_ascii=False, separators=(',', ':')).encode("utf-8")

            # 构建消息头
            headers = {
                "event": "unhandled.message.v1",
                "wxid": wxid,
                "msg_id": str(message_data.get("msg_id", "unknown")),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            message = aio_pika.Message(
                body=message_body,
                headers=headers,
                **self.message_properties
            )

            # 发布消息到队列 - 使用默认交换机提高效率
            await self.channel.default_exchange.publish(
                message,
                routing_key=queue_name
            )

            logger.debug(f"Published unhandled message to queue {queue_name}: msg_id={message_data.get('msg_id', 'unknown')}")
            return True

        except Exception as e:
            logger.error(f"Failed to publish unhandled message to wxid queue {wxid}: {e}")
            return False


# 全局发布器实例
_global_publisher: Optional[UnifiedMQPublisher] = None


async def init_mq_publisher():
    """初始化全局MQ发布器"""
    global _global_publisher
    _global_publisher = UnifiedMQPublisher()
    await _global_publisher.connect()


async def close_mq_publisher():
    """关闭全局MQ发布器"""
    global _global_publisher
    if _global_publisher:
        await _global_publisher.disconnect()
        _global_publisher = None


def get_mq_publisher() -> Optional[UnifiedMQPublisher]:
    """获取全局MQ发布器"""
    return _global_publisher


# 向后兼容的别名
async def init_product_match_publisher():
    """初始化全局商品匹配发布器（向后兼容）"""
    await init_mq_publisher()


async def close_product_match_publisher():
    """关闭全局商品匹配发布器（向后兼容）"""
    await close_mq_publisher()


def get_product_match_publisher() -> Optional[UnifiedMQPublisher]:
    """获取全局商品匹配发布器（向后兼容）"""
    return get_mq_publisher()


async def publish_product_matches(
    ts: str,
    msg_id: int,
    store: Dict[str, Any],
    products: List[Dict[str, Any]]
) -> bool:
    """
    发布商品匹配结果（便捷函数）
    """
    publisher = get_mq_publisher()
    if not publisher:
        logger.error("MQ publisher not initialized")
        return False

    return await publisher.publish_product_matches(ts, msg_id, store, products)


async def publish_unhandled_message(
    message_data: Dict[str, Any],
    wxid: str
) -> bool:
    """
    发布未处理消息（便捷函数）

    Args:
        message_data: 消息数据
        wxid: 微信ID

    Returns:
        bool: 是否发布成功
    """
    publisher = get_mq_publisher()
    if not publisher:
        logger.error("MQ publisher not initialized")
        return False

    return await publisher.publish_unhandled_message(message_data, wxid)
