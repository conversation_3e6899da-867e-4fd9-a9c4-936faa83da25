# V1/V2/V3 API 接口验证报告

## 验证概述

本报告验证了 V1、V2、V3 三个版本的微信 API 适配器实现是否符合各自的文档说明。

## V1 API 验证结果

### 基础信息
- **文档参考**: `http://8.133.252.208:8080/docs/swagger.json`
- **基础URL**: `http://8.133.252.208:8080`
- **认证方式**: URL 参数 `key`

### 接口验证

#### ✅ 消息发送接口
1. **发送文本消息**
   - 路径: `/message/SendMessage` ✅
   - 字段: `MsgItem[].MsgType=1, ToUserName, Content, At` ✅
   - At 字段: 逗号分隔的 wxid 列表 ✅

2. **发送图片消息**
   - 路径: `/message/SendMessage` ✅
   - 字段: `MsgItem[].MsgType=2, ImageContent` ✅

3. **发送应用消息**
   - 路径: `/message/SendAppMessage` ✅
   - 字段: `AppList[].ToUserName, ContentType, ContentXML` ✅

4. **发送语音消息**
   - 路径: `/message/SendUploadVoice` ✅
   - 字段: `ToUserName, VoiceData, VoiceFormat, VoiceSecond` ✅
   - 时间转换: ms → 秒 ✅

5. **分享名片**
   - 路径: `/message/ShareCard` ✅
   - 字段: `ToUserName, CardWxId, CardNickName, CardAlias` ✅

6. **撤回消息**
   - 路径: `/message/RevokeMsg` ✅
   - 字段: `ToUserName, ClientMsgId, CreateTime, NewMsgId` ✅

#### ✅ 好友管理接口
1. **同意好友申请**
   - 路径: `/friend/PassVerify` ✅
   - 字段: `V1, V2, Scene` ✅

2. **获取用户OpenID**
   - 路径: `/applet/GetUserOpenId` ✅
   - 字段: `Appid, ToUserName` ✅

#### ✅ 群组管理接口
1. **邀请群成员**
   - 路径: `/group/InviteChatroomMembers` ✅
   - 字段: `ChatRoomName, UserList` ✅

2. **删除群成员**
   - 路径: `/group/DelChatRoomMember` ✅
   - 字段: `ChatRoomName, UserList` ✅

3. **获取群信息**
   - 路径: `/group/GetChatroomInfo` ✅
   - 字段: `ChatRoomName` ✅

4. **获取群成员详情**
   - 路径: `/group/GetChatroomMemberDetail` ✅
   - 字段: `ChatRoomName` ✅

#### ✅ 其他接口
1. **确认收款**
   - 路径: `/pay/Collectmoney` ✅
   - 字段: `ToUserName, TransFerId, TransactionId` ✅

2. **获取在线状态**
   - 路径: `/login/GetCacheInfo` ✅
   - 参数: `wxid` (query 参数) ✅

3. **上传图片到CDN**
   - 路径: `/sns/UploadFriendCircleImage` ✅
   - 字段: `Base64` ✅

## V2 API 验证结果

### 基础信息
- **文档参考**: `http://8.133.252.208:8060/swagger.json (basePath=/api)`
- **基础URL**: `http://8.133.252.208:8060/api`
- **认证方式**: URL 参数 `key`

### 接口验证

#### ✅ 消息发送接口
1. **发送文本消息**
   - 路径: `/Msg/SendTxt` ✅
   - 字段: `Wxid, ToWxid, Content, Type=1, At` ✅

2. **发送图片消息**
   - 路径: `/Msg/UploadImg` ✅
   - 字段: `Wxid, ToWxid, Base64` ✅

3. **发送应用消息**
   - 路径: `/Msg/SendApp` ✅
   - 字段: `Wxid, ToWxid, Type, Xml` ✅

4. **发送语音消息**
   - 路径: `/Msg/SendVoice` ✅
   - 字段: `Wxid, ToWxid, Base64, Type, VoiceTime` ✅

5. **分享名片**
   - 路径: `/Msg/ShareCard` ✅
   - 字段: `Wxid, ToWxid, CardWxId, CardNickName, CardAlias` ✅

6. **撤回消息**
   - 路径: `/Msg/Revoke` ✅
   - 字段: `Wxid, ToUserName, ClientMsgId, CreateTime, NewMsgId` ✅

#### ✅ 好友管理接口
1. **同意好友申请**
   - 路径: `/Friend/PassVerify` ✅
   - 字段: `Wxid, V1, V2, Scene` ✅

2. **获取用户OpenID**
   - 路径: `/Wxapp/GetUserOpenId` ✅
   - 字段: `Wxid, Appid, ToWxId` ✅

#### ✅ 群组管理接口
1. **邀请群成员**
   - 路径: `/Group/InviteChatRoomMember` ✅
   - 字段: `Wxid, ChatRoomName, ToWxids` (逗号分隔) ✅

2. **删除群成员**
   - 路径: `/Group/DelChatRoomMember` ✅
   - 字段: `Wxid, ChatRoomName, ToWxids` (逗号分隔) ✅

3. **获取群信息**
   - 路径: `/Group/GetChatRoomInfoDetail` ✅
   - 字段: `Wxid, QID` ✅

4. **获取群成员详情**
   - 路径: `/Group/GetChatRoomMemberDetail` ✅
   - 字段: `Wxid, QID` ✅

#### ✅ 其他接口
1. **确认收款**
   - 路径: `/TenPay/Collectmoney` ✅
   - 字段: `Wxid, ToUserName, TransFerId, TransactionId, InvalidTime` ✅

2. **获取在线状态**
   - 路径: `/Login/GetCacheInfo` ✅
   - 参数: `wxid` (query 参数) ✅

3. **上传图片到CDN**
   - 路径: `/Msg/UploadImg` ✅ (复用发送图片接口)
   - 字段: `Wxid, ToWxid, Base64` ✅

## V3 API 验证结果

### 基础信息
- **文档参考**: `http://doc.geweapi.com/`
- **基础URL**: `http://api.geweapi.com/gewe/v2/api`
- **认证方式**: 请求头 `X-GEWE-TOKEN`
- **应用标识**: 请求体 `appId`

### 接口验证

#### ✅ 消息发送接口 (通过 gewechat_client)
1. **发送文本消息**
   - 路径: `/message/postText` ✅
   - 字段: `appId, toWxid, content, ats` ✅
   - 实现: `client.post_text(app_id, to_wxid, content, ats)` ✅

2. **发送图片消息**
   - 路径: `/message/postImage` ✅
   - 字段: `appId, toWxid, imgUrl` ✅
   - 实现: `client.post_image(app_id, to_wxid, img_url)` ✅
   - 验证: 仅接受 HTTP/HTTPS URL ✅

3. **发送应用消息**
   - 路径: `/message/postAppMsg` ✅
   - 字段: `appId, toWxid, appmsg` ✅
   - 实现: `client.post_app_msg(app_id, to_wxid, appmsg)` ✅

4. **发送语音消息**
   - 路径: `/message/postVoice` ✅
   - 字段: `appId, toWxid, voiceUrl, voiceDuration` ✅
   - 实现: `client.post_voice(app_id, to_wxid, voice_url, voice_duration)` ✅
   - 验证: 仅接受 HTTP/HTTPS URL ✅

5. **分享名片**
   - 路径: `/message/postNameCard` ✅
   - 字段: `appId, toWxid, nickName, nameCardWxid` ✅
   - 实现: `client.post_name_card(app_id, to_wxid, nick_name, name_card_wxid)` ✅

6. **撤回消息**
   - 路径: `/message/revokeMsg` ✅
   - 字段: `appId, toWxid, msgId, newMsgId, createTime` ✅
   - 实现: `client.revoke_msg(app_id, to_wxid, msg_id, new_msg_id, create_time)` ✅

7. **转发小程序**
   - 路径: `/message/forwardMiniApp` ✅
   - 字段: `appId, toWxid, xml, coverImgUrl` ✅
   - 实现: `client.forward_mini_app(app_id, to_wxid, xml, cover_img_url)` ✅

#### ✅ 好友管理接口
1. **同意好友申请**
   - 路径: `/contacts/addContacts` ✅
   - 字段: `appId, scene, option, v3, v4, content` ✅
   - 实现: `client.add_contacts(app_id, scene, option, v3, v4, content)` ✅
   - 模型扩展: 支持 V1/V2/V3 字段 ✅

2. **获取用户OpenID**
   - 状态: 暂不支持 ⚠️ (GeWe 平台未提供等价接口)

#### ✅ 群组管理接口
1. **邀请群成员**
   - 路径: `/group/inviteMember` ✅
   - 字段: `appId, wxids, chatroomId, reason` ✅
   - 实现: `client.invite_member(app_id, wxids, chatroom_id, reason)` ✅

2. **删除群成员**
   - 路径: `/group/removeMember` ✅
   - 字段: `appId, wxids, chatroomId` ✅
   - 实现: `client.remove_member(app_id, wxids, chatroom_id)` ✅

3. **获取群信息**
   - 路径: `/group/getChatroomInfo` ✅
   - 字段: `appId, chatroomId` ✅
   - 实现: `client.get_chatroom_info(app_id, chatroom_id)` ✅

4. **获取群成员详情**
   - 路径: `/group/getChatroomMemberDetail` ✅
   - 字段: `appId, chatroomId, memberWxids` ✅
   - 实现: `client.get_chatroom_member_detail(app_id, chatroom_id, member_wxids)` ✅

#### ✅ 其他接口
1. **确认收款**
   - 状态: 暂不支持 ⚠️ (GeWe 文档中未找到等价接口)

2. **获取在线状态**
   - 路径: `/login/checkOnline` ✅
   - 字段: `appId` ✅
   - 实现: `client.check_online(app_id)` ✅

3. **上传图片到CDN**
   - 状态: 暂不支持 ⚠️ (V3 平台不提供直接 base64 上传CDN接口)

## 发现的问题与建议

### ⚠️ 需要注意的差异

1. **V3 图片/语音接口限制**
   - V1/V2: 支持 base64 数据
   - V3: 仅支持 HTTP/HTTPS URL
   - 建议: 实现自动桥接（base64 → 上传 → URL）

2. **V3 部分接口缺失**
   - `get_user_openid`: GeWe 平台未提供
   - `confirm_collection`: GeWe 文档中未找到
   - `upload_image_to_cdn`: 需要外部存储支持

3. **字段命名差异**
   - V1: `ToUserName`, `ChatRoomName`
   - V2: `ToWxid`, `ChatRoomName`
   - V3: `toWxid`, `chatroomId`
   - 解决: 统一模型内部映射 ✅

### ✅ 验证通过的特性

1. **统一接口设计**
   - 所有版本使用相同的方法签名 ✅
   - 内部自动处理字段映射 ✅
   - 错误处理统一 ✅

2. **认证机制**
   - V1/V2: URL 参数 `key` ✅
   - V3: 请求头 `X-GEWE-TOKEN` + 请求体 `appId` ✅

3. **重试机制**
   - 所有版本支持指数退避重试 ✅
   - 4xx 错误不重试，5xx 错误重试 ✅

4. **测试覆盖**
   - 路径验证 ✅
   - 字段映射验证 ✅
   - 错误处理验证 ✅

## 总结

- **V1 API**: 完全符合文档规范 ✅
- **V2 API**: 完全符合文档规范 ✅  
- **V3 API**: 基本符合 GeWe 文档规范，通过 gewechat_client 实现 ✅
- **统一接口**: 成功抽象三版本差异，提供一致的调用体验 ✅

所有接口实现均已通过单元测试验证，符合各自版本的 API 文档说明。
