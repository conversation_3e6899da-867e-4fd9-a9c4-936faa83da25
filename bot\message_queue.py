"""
消息队列管理器
处理消息队列的订阅和消息分发
"""
import json
import time
import threading
from typing import Dict, Any, Callable, Optional
from dataclasses import dataclass
from loguru import logger

try:
    import pika
    PIKA_AVAILABLE = True
except ImportError:
    PIKA_AVAILABLE = False
    logger.warning("pika not available, using mock message queue")


@dataclass
class MessageQueueConfig:
    """消息队列配置"""
    host: str = "localhost"
    port: int = 5672
    username: str = "guest"
    password: str = "guest"
    virtual_host: str = "/"
    exchange: str = "wechat_messages"
    queue_prefix: str = "group_"
    routing_key_prefix: str = "group."


class MockMessageQueue:
    """模拟消息队列（用于测试）"""
    
    def __init__(self, group_name: str, config: MessageQueueConfig):
        self.group_name = group_name
        self.config = config
        self.is_running = False
        self.message_handler = None
        self.mock_thread = None
        
        logger.info(f"初始化模拟消息队列: {group_name}")
    
    def start_consuming(self, message_handler: Callable[[Dict[str, Any]], None]):
        """开始消费消息"""
        self.message_handler = message_handler
        self.is_running = True
        
        # 启动模拟消息生成线程
        self.mock_thread = threading.Thread(target=self._generate_mock_messages, daemon=True)
        self.mock_thread.start()
        
        logger.info(f"模拟消息队列 {self.group_name} 开始消费")
    
    def _generate_mock_messages(self):
        """生成模拟消息"""
        message_count = 0
        while self.is_running:
            time.sleep(30)  # 每30秒生成一条模拟消息
            
            if self.message_handler:
                mock_message = {
                    "type": "send_text",
                    "to_wxid": "YBA-19990312",
                    "content": f"模拟消息 #{message_count + 1} 来自组 {self.group_name}",
                    "timestamp": time.time(),
                    "message_id": f"mock_{self.group_name}_{message_count}"
                }
                
                try:
                    self.message_handler(mock_message)
                    message_count += 1
                except Exception as e:
                    logger.error(f"处理模拟消息失败: {e}")
    
    def stop_consuming(self):
        """停止消费消息"""
        self.is_running = False
        if self.mock_thread:
            self.mock_thread.join(timeout=5)
        logger.info(f"模拟消息队列 {self.group_name} 停止消费")
    
    def publish_message(self, message: Dict[str, Any]):
        """发布消息（模拟）"""
        logger.info(f"模拟发布消息到 {self.group_name}: {message}")


class RabbitMQConsumer:
    """RabbitMQ消费者"""
    
    def __init__(self, group_name: str, config: MessageQueueConfig):
        self.group_name = group_name
        self.config = config
        self.connection = None
        self.channel = None
        self.queue_name = f"{config.queue_prefix}{group_name}"
        self.routing_key = f"{config.routing_key_prefix}{group_name}"
        self.is_running = False
        
        logger.info(f"初始化RabbitMQ消费者: {group_name}")
    
    def _connect(self):
        """连接到RabbitMQ"""
        try:
            credentials = pika.PlainCredentials(self.config.username, self.config.password)
            parameters = pika.ConnectionParameters(
                host=self.config.host,
                port=self.config.port,
                virtual_host=self.config.virtual_host,
                credentials=credentials
            )
            
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # 声明交换机
            self.channel.exchange_declare(
                exchange=self.config.exchange,
                exchange_type='topic',
                durable=True
            )
            
            # 声明队列
            self.channel.queue_declare(queue=self.queue_name, durable=True)
            
            # 绑定队列到交换机
            self.channel.queue_bind(
                exchange=self.config.exchange,
                queue=self.queue_name,
                routing_key=self.routing_key
            )
            
            logger.info(f"RabbitMQ连接成功: {self.group_name}")
            
        except Exception as e:
            logger.error(f"RabbitMQ连接失败: {e}")
            raise
    
    def start_consuming(self, message_handler: Callable[[Dict[str, Any]], None]):
        """开始消费消息"""
        try:
            self._connect()
            
            def callback(ch, method, properties, body):
                try:
                    message = json.loads(body.decode('utf-8'))
                    logger.debug(f"收到消息: {message}")
                    
                    # 处理消息
                    message_handler(message)
                    
                    # 确认消息
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                    
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    # 拒绝消息并重新入队
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            
            self.channel.basic_qos(prefetch_count=1)
            self.channel.basic_consume(queue=self.queue_name, on_message_callback=callback)
            
            self.is_running = True
            logger.info(f"开始消费消息: {self.group_name}")
            
            self.channel.start_consuming()
            
        except Exception as e:
            logger.error(f"消费消息失败: {e}")
            self.stop_consuming()
    
    def stop_consuming(self):
        """停止消费消息"""
        self.is_running = False
        
        if self.channel and not self.channel.is_closed:
            self.channel.stop_consuming()
            self.channel.close()
        
        if self.connection and not self.connection.is_closed:
            self.connection.close()
        
        logger.info(f"停止消费消息: {self.group_name}")
    
    def publish_message(self, message: Dict[str, Any]):
        """发布消息"""
        try:
            if not self.connection or self.connection.is_closed:
                self._connect()
            
            self.channel.basic_publish(
                exchange=self.config.exchange,
                routing_key=self.routing_key,
                body=json.dumps(message, ensure_ascii=False),
                properties=pika.BasicProperties(delivery_mode=2)  # 持久化消息
            )
            
            logger.debug(f"发布消息成功: {message}")
            
        except Exception as e:
            logger.error(f"发布消息失败: {e}")


class MessageQueueManager:
    """消息队列管理器"""
    
    def __init__(self, config: MessageQueueConfig = None, use_mock: bool = None):
        """
        初始化消息队列管理器
        
        Args:
            config: 消息队列配置
            use_mock: 是否使用模拟队列，None时自动检测
        """
        self.config = config or MessageQueueConfig()
        self.use_mock = use_mock if use_mock is not None else not PIKA_AVAILABLE
        self.consumers: Dict[str, Any] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        if self.use_mock:
            logger.info("使用模拟消息队列")
        else:
            logger.info("使用RabbitMQ消息队列")
    
    def create_consumer(self, group_name: str) -> Any:
        """创建消费者"""
        if self.use_mock:
            return MockMessageQueue(group_name, self.config)
        else:
            return RabbitMQConsumer(group_name, self.config)
    
    def start_group_consumer(self, group_name: str, message_handler: Callable[[Dict[str, Any]], None]):
        """
        启动组消息消费者
        
        Args:
            group_name: 组名
            message_handler: 消息处理函数
        """
        if group_name in self.consumers:
            logger.warning(f"组 {group_name} 的消费者已存在")
            return
        
        try:
            consumer = self.create_consumer(group_name)
            self.consumers[group_name] = consumer
            self.message_handlers[group_name] = message_handler
            
            # 在单独线程中启动消费
            def start_consuming():
                try:
                    consumer.start_consuming(message_handler)
                except Exception as e:
                    logger.error(f"组 {group_name} 消费者启动失败: {e}")
            
            thread = threading.Thread(target=start_consuming, daemon=True)
            thread.start()
            
            logger.info(f"启动组 {group_name} 消息消费者")
            
        except Exception as e:
            logger.error(f"创建组 {group_name} 消费者失败: {e}")
    
    def stop_group_consumer(self, group_name: str):
        """停止组消息消费者"""
        consumer = self.consumers.get(group_name)
        if consumer:
            try:
                consumer.stop_consuming()
                del self.consumers[group_name]
                del self.message_handlers[group_name]
                logger.info(f"停止组 {group_name} 消息消费者")
            except Exception as e:
                logger.error(f"停止组 {group_name} 消费者失败: {e}")
    
    def publish_to_group(self, group_name: str, message: Dict[str, Any]):
        """向指定组发布消息"""
        consumer = self.consumers.get(group_name)
        if consumer:
            try:
                consumer.publish_message(message)
            except Exception as e:
                logger.error(f"向组 {group_name} 发布消息失败: {e}")
        else:
            logger.warning(f"组 {group_name} 的消费者不存在")
    
    def get_consumer_stats(self) -> Dict[str, Any]:
        """获取消费者统计信息"""
        return {
            'total_consumers': len(self.consumers),
            'active_groups': list(self.consumers.keys()),
            'use_mock': self.use_mock,
            'config': {
                'host': self.config.host,
                'port': self.config.port,
                'exchange': self.config.exchange,
                'queue_prefix': self.config.queue_prefix
            }
        }
    
    def shutdown(self):
        """关闭所有消费者"""
        logger.info("正在关闭消息队列管理器...")
        
        for group_name in list(self.consumers.keys()):
            self.stop_group_consumer(group_name)
        
        logger.info("消息队列管理器已关闭")
