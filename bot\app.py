#!/usr/bin/env python3
"""
微信API应用程序主入口
支持多组账号管理和消息队列处理
"""
import os
import sys
import signal
import argparse
import time
import threading
from typing import Dict, Any
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from group_manager import GroupManager
from message_queue import MessageQueueManager, MessageQueueConfig
from send_adapter.models import SendTextRequest, SendImageRequest, APIResponse


class WechatApp:
    """微信API应用程序"""
    
    def __init__(self, group_name: str = None, use_mock_db: bool = True, use_mock_mq: bool = True):
        """
        初始化应用程序
        
        Args:
            group_name: 指定的组名，None表示管理所有组
            use_mock_db: 是否使用模拟数据库
            use_mock_mq: 是否使用模拟消息队列
        """
        self.group_name = group_name
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # 初始化组件
        self.db_manager = DatabaseManager(use_mock=use_mock_db)
        self.group_manager = GroupManager(self.db_manager)
        
        # 消息队列配置
        mq_config = MessageQueueConfig(
            host=os.getenv('MQ_HOST', 'localhost'),
            port=int(os.getenv('MQ_PORT', 5672)),
            username=os.getenv('MQ_USERNAME', 'guest'),
            password=os.getenv('MQ_PASSWORD', 'guest'),
            exchange=os.getenv('MQ_EXCHANGE', 'wechat_messages'),
            queue_prefix=os.getenv('MQ_QUEUE_PREFIX', 'group_')
        )
        self.mq_manager = MessageQueueManager(config=mq_config, use_mock=use_mock_mq)
        
        # 统计信息
        self.stats = {
            'start_time': time.time(),
            'messages_processed': 0,
            'messages_sent': 0,
            'errors': 0
        }
        
        logger.info(f"微信API应用程序初始化完成，目标组: {group_name or '所有组'}")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _handle_message(self, group_name: str, message: Dict[str, Any]):
        """
        处理接收到的消息
        
        Args:
            group_name: 组名
            message: 消息内容
        """
        try:
            self.stats['messages_processed'] += 1
            
            message_type = message.get('type')
            logger.info(f"处理组 {group_name} 的消息: {message_type}")
            
            response = None
            
            if message_type == 'send_text':
                # 发送文本消息
                request = SendTextRequest(
                    wxid=message.get('wxid', 'auto'),  # 会被组适配器自动选择
                    to_wxid=message.get('to_wxid'),
                    content=message.get('content'),
                    at=message.get('at', [])
                )
                response = self.group_manager.send_text_to_group(group_name, request)
                
            elif message_type == 'send_image':
                # 发送图片消息
                request = SendImageRequest(
                    wxid=message.get('wxid', 'auto'),
                    to_wxid=message.get('to_wxid'),
                    image_base64=message.get('image_url')  # V3需要URL
                )
                response = self.group_manager.send_image_to_group(group_name, request)
                
            elif message_type == 'health_check':
                # 健康检查
                stats = self.group_manager.get_group_stats(group_name)
                response = APIResponse(ok=True, data=stats)
                
            else:
                logger.warning(f"未知的消息类型: {message_type}")
                response = APIResponse(ok=False, message=f"未知的消息类型: {message_type}")
            
            # 更新统计
            if response and response.ok:
                self.stats['messages_sent'] += 1
                logger.info(f"消息处理成功: {message.get('message_id', 'unknown')}")
            else:
                self.stats['errors'] += 1
                logger.error(f"消息处理失败: {response.message if response else 'unknown error'}")
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"处理消息异常: {e}")
    
    def _start_group_consumer(self, group_name: str):
        """启动指定组的消息消费者"""
        def message_handler(message: Dict[str, Any]):
            self._handle_message(group_name, message)
        
        self.mq_manager.start_group_consumer(group_name, message_handler)
    
    def _initialize_groups(self):
        """初始化组"""
        if self.group_name:
            # 指定组模式
            groups = [self.group_name]
        else:
            # 所有组模式
            groups = self.db_manager.get_all_groups()
        
        if not groups:
            logger.warning("没有找到可用的组")
            return
        
        logger.info(f"初始化组: {groups}")
        
        for group in groups:
            try:
                # 创建组适配器
                group_adapter = self.group_manager.get_or_create_group_adapter(group)
                if group_adapter:
                    logger.info(f"组 {group} 初始化成功，可用账号: {group_adapter.get_available_accounts_count()}")
                    
                    # 启动消息消费者
                    self._start_group_consumer(group)
                else:
                    logger.warning(f"组 {group} 初始化失败")
                    
            except Exception as e:
                logger.error(f"初始化组 {group} 失败: {e}")
    
    def _health_check_loop(self):
        """健康检查循环"""
        while not self.shutdown_event.is_set():
            try:
                # 执行健康检查
                health_status = self.group_manager.health_check()
                
                unhealthy_groups = [group for group, status in health_status.items() if not status]
                if unhealthy_groups:
                    logger.warning(f"不健康的组: {unhealthy_groups}")
                
                # 等待下次检查
                self.shutdown_event.wait(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                self.shutdown_event.wait(10)
    
    def _stats_loop(self):
        """统计信息输出循环"""
        while not self.shutdown_event.is_set():
            try:
                # 输出统计信息
                uptime = time.time() - self.stats['start_time']
                logger.info(
                    f"运行统计 - 运行时间: {uptime:.0f}s, "
                    f"处理消息: {self.stats['messages_processed']}, "
                    f"发送成功: {self.stats['messages_sent']}, "
                    f"错误: {self.stats['errors']}"
                )
                
                # 输出组统计
                all_stats = self.group_manager.get_all_stats()
                logger.info(
                    f"组统计 - 总组数: {all_stats['total_groups']}, "
                    f"健康组数: {all_stats['healthy_groups']}, "
                    f"总账号: {all_stats['total_accounts']}, "
                    f"可用账号: {all_stats['available_accounts']}"
                )
                
                # 等待下次输出
                self.shutdown_event.wait(300)  # 每5分钟输出一次
                
            except Exception as e:
                logger.error(f"统计输出异常: {e}")
                self.shutdown_event.wait(60)
    
    def start(self):
        """启动应用程序"""
        if self.is_running:
            logger.warning("应用程序已在运行")
            return
        
        logger.info("启动微信API应用程序...")
        
        try:
            # 设置信号处理器
            self._setup_signal_handlers()
            
            # 初始化组
            self._initialize_groups()
            
            # 启动后台线程
            health_thread = threading.Thread(target=self._health_check_loop, daemon=True)
            health_thread.start()
            
            stats_thread = threading.Thread(target=self._stats_loop, daemon=True)
            stats_thread.start()
            
            self.is_running = True
            logger.info("微信API应用程序启动成功")
            
            # 主循环
            while not self.shutdown_event.is_set():
                self.shutdown_event.wait(1)
            
        except Exception as e:
            logger.error(f"应用程序启动失败: {e}")
            self.shutdown()
    
    def shutdown(self):
        """关闭应用程序"""
        if not self.is_running:
            return
        
        logger.info("正在关闭微信API应用程序...")
        
        # 设置关闭标志
        self.shutdown_event.set()
        self.is_running = False
        
        try:
            # 关闭消息队列
            self.mq_manager.shutdown()
            
            # 关闭组管理器
            self.group_manager.shutdown()
            
            # 关闭数据库连接
            self.db_manager.close()
            
            logger.info("微信API应用程序已关闭")
            
        except Exception as e:
            logger.error(f"关闭应用程序时出错: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取应用程序状态"""
        uptime = time.time() - self.stats['start_time']
        
        return {
            'is_running': self.is_running,
            'group_name': self.group_name,
            'uptime': uptime,
            'stats': self.stats.copy(),
            'group_stats': self.group_manager.get_all_stats(),
            'mq_stats': self.mq_manager.get_consumer_stats()
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信API应用程序')
    parser.add_argument('--group', type=str, help='指定组名（如：YBA）')
    parser.add_argument('--mock-db', action='store_true', help='使用模拟数据库')
    parser.add_argument('--mock-mq', action='store_true', help='使用模拟消息队列')
    parser.add_argument('--log-level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 创建应用程序
    app = WechatApp(
        group_name=args.group,
        use_mock_db=args.mock_db,
        use_mock_mq=args.mock_mq
    )
    
    try:
        # 启动应用程序
        app.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    finally:
        app.shutdown()


if __name__ == "__main__":
    main()
