#!/usr/bin/env python3
"""
V3 接口发送文本消息示例
使用统一的 send_adapter 接口发送消息给指定用户
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import SendTextRequest
from send_adapter.exceptions import APIValidationError, APIRequestError


def get_v3_config():
    """获取 V3 API 配置参数"""
    # 直接返回配置字典，无需环境变量
    return {
        "v3_base_url": "http://api.geweapi.com/gewe/v2/api",
        "gewe_token": "your_gewe_token_here",  # 🔑 替换为你的 GeWe Token
        "gewe_appid": "your_app_id_here"       # 🔑 替换为你的 App ID
    }


def send_text_message_v3():
    """使用 V3 接口发送文本消息的示例"""

    # 1. 获取配置参数
    config = get_v3_config()

    # 2. 创建 V3 适配器（直接传入配置）
    try:
        adapter = create_adapter("v3", **config)
        print("✅ V3 适配器创建成功")
    except Exception as e:
        print(f"❌ 创建适配器失败: {e}")
        return False
    
    # 3. 构造发送文本消息的请求
    request = SendTextRequest(
        wxid="your_bot_wxid",        # 替换为你的机器人微信ID
        to_wxid="YBA-19990312",      # 目标用户微信ID
        content="Hello! 这是通过 V3 接口发送的测试消息 🚀",
        at=None  # 私聊消息不需要 @
    )
    
    # 4. 发送消息
    try:
        print(f"📤 正在发送消息给 {request.to_wxid}...")
        response = adapter.send_text(request)
        
        if response.ok:
            print("✅ 消息发送成功!")
            print(f"📋 响应数据: {response.data}")
            return True
        else:
            print(f"❌ 消息发送失败: {response.message}")
            return False
            
    except APIValidationError as e:
        print(f"❌ 参数验证错误: {e}")
        print("💡 提示: 请检查 GEWE_TOKEN 和 GEWE_APPID 是否正确配置")
        return False
        
    except APIRequestError as e:
        print(f"❌ API 请求错误: {e}")
        print("💡 提示: 请检查网络连接和 V3_BASE_URL 配置")
        return False
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


def send_group_message_v3():
    """使用 V3 接口发送群消息的示例（包含 @ 功能）"""

    # 创建适配器（传入配置）
    config = get_v3_config()
    adapter = create_adapter("v3", **config)
    
    # 构造群消息请求
    request = SendTextRequest(
        wxid="your_bot_wxid",           # 替换为你的机器人微信ID
        to_wxid="group_chat_id",        # 替换为群聊ID
        content="@YBA-19990312 你好！这是一条群消息测试",
        at=["YBA-19990312"]             # @ 指定用户
    )
    
    try:
        print(f"📤 正在发送群消息...")
        response = adapter.send_text(request)
        
        if response.ok:
            print("✅ 群消息发送成功!")
            return True
        else:
            print(f"❌ 群消息发送失败: {response.message}")
            return False
            
    except Exception as e:
        print(f"❌ 发送群消息时出错: {e}")
        return False


def main():
    """主函数"""
    print("🚀 V3 接口发送文本消息示例")
    print("=" * 50)
    
    # 检查配置参数
    config = get_v3_config()
    placeholder_values = ["your_gewe_token_here", "your_app_id_here"]

    if any(config[key] in placeholder_values for key in ["gewe_token", "gewe_appid"]):
        print("⚠️  警告: 检测到占位符配置值")
        print("💡 使用前请在 get_v3_config() 函数中设置正确的值:")
        print(f"   - gewe_token: {config['gewe_token']}")
        print(f"   - gewe_appid: {config['gewe_appid']}")
        print()
    
    # 发送私聊消息
    print("1️⃣ 发送私聊消息示例:")
    success = send_text_message_v3()
    
    if success:
        print("\n2️⃣ 发送群消息示例:")
        send_group_message_v3()
    
    print("\n" + "=" * 50)
    print("📖 使用说明:")
    print("1. 在 get_v3_config() 函数中替换占位符值为你的实际配置")
    print("2. 确保 GeWe 平台服务正常运行")
    print("3. 确保机器人已登录并有发送消息的权限")
    print("4. 无需设置环境变量，直接在代码中配置即可")


if __name__ == "__main__":
    main()
