"""V3 版本微信API适配器实现
V3 文档: http://doc.geweapi.com/
基于 GeWe 开放平台接口说明进行映射（非 swagger）。
"""
from __future__ import annotations
import time
from typing import Dict, Any
import requests
from loguru import logger

from .base import WechatAPIAdapter
from .config import send_settings
from .exceptions import APIRequestError, APIServerError, APIValidationError
from .gewechat_client import GewechatClient
from .models import (
    APIResponse,
    SendTextRequest, SendImageRequest, SendAppRequest, SendVoiceRequest,
    ShareCardRequest, RevokeMessageRequest, UploadImageToCDNRequest,
    AcceptFriendRequest, GetUserOpenidRequest,
    InviteGroupMemberRequest, RemoveGroupMemberRequest,
    GetGroupInfoRequest, GetGroupMembersRequest,
    ConfirmCollectionRequest, GetOnlineStatusRequest,
    ForwardMiniAppRequest,
)
from .account import WechatAccount


class WechatAPIV3Adapter(WechatAPIAdapter):
    def __init__(self, account: WechatAccount = None, **config):
        # 支持账号对象或传统配置方式
        if account:
            self.account = account
            self.base_url = account.base_url.rstrip("/")
            self.timeout = account.timeout
            self.max_retries = account.max_retries
            self.backoff_factor = account.backoff_factor
            self.gewe_token = account.get_gewe_token()
            self.gewe_appid = account.get_gewe_appid()
        else:
            # 向后兼容：支持传入配置参数
            self.account = None
            self.base_url = config.get("v3_base_url", send_settings.v3_base_url).rstrip("/")
            self.timeout = config.get("request_timeout", send_settings.request_timeout)
            self.max_retries = config.get("max_retries", send_settings.max_retries)
            self.backoff_factor = config.get("backoff_factor", send_settings.backoff_factor)
            self.gewe_token = config.get("gewe_token", send_settings.gewe_token)
            self.gewe_appid = config.get("gewe_appid", send_settings.gewe_appid)

        # 初始化 GeWeChat 客户端（官方封装）
        self._client = GewechatClient(self.base_url, self.gewe_token)


    def _require_app_id(self) -> str:
        if not self.gewe_appid:
            raise APIValidationError("V3 API 需要配置 GEWE_APPID")
        return self.gewe_appid

    def _headers(self) -> Dict[str, str]:
        headers: Dict[str, str] = {"Content-Type": "application/json"}
        if self.gewe_token:
            # 文档示例使用 X-GEWE-TOKEN
            headers["X-GEWE-TOKEN"] = self.gewe_token
        return headers

    def _post(self, path: str, json: Dict[str, Any] | None = None, params: Dict[str, Any] | None = None) -> APIResponse:
        url = f"{self.base_url}{path}"
        merged_params = dict(params or {})

        attempt = 0
        last_exc: Exception | None = None
        while attempt < self.max_retries:
            try:
                logger.debug(f"POST {url} json={json} params={merged_params}")
                resp = requests.post(url, json=json, params=merged_params, headers=self._headers(), timeout=self.timeout)
                if 500 <= resp.status_code < 600:
                    raise APIServerError(f"Server error {resp.status_code}", status_code=resp.status_code)
                if resp.status_code != 200:
                    raise APIRequestError(f"HTTP {resp.status_code}: {resp.text}", status_code=resp.status_code)
                try:
                    payload = resp.json()
                except Exception:
                    payload = {"raw": resp.text}
                return APIResponse(ok=True, code=resp.status_code, data=payload)
            except (requests.Timeout, requests.ConnectionError, APIServerError) as e:
                last_exc = e
                attempt += 1
                sleep_s = self.backoff_factor * (2 ** (attempt - 1))
                logger.warning(f"Request failed (attempt {attempt}/{self.max_retries}): {e}. Backing off {sleep_s:.2f}s")
                time.sleep(sleep_s)
            except APIRequestError as e:
                logger.error(f"Non-retryable HTTP error: {e}")
                raise
            except Exception as e:
                last_exc = e
                logger.error(f"Unexpected error: {e}")
                attempt += 1
                time.sleep(self.backoff_factor)
        raise APIRequestError(f"Request failed after {self.max_retries} attempts: {last_exc}")

    # ---- 消息发送 ----
    def send_text(self, req: SendTextRequest) -> APIResponse:
        app_id = self._require_app_id()
        ats = ",".join(req.at) if req.at else ""
        result = self._client.post_text(app_id, req.to_wxid, req.content, ats)
        return APIResponse(ok=True, data=result)

    def send_image(self, req: SendImageRequest) -> APIResponse:
        # 统一接口接受 base64 或 URL：V3 文档要求 URL，这里仅当为 URL 时发送
        img = req.image_base64
        if not (isinstance(img, str) and img.lower().startswith(("http://", "https://"))):
            raise APIValidationError("V3 postImage 需要 imgUrl（http/https），请传可访问的图片链接或改用 V1/V2 发送base64")
        app_id = self._require_app_id()
        result = self._client.post_image(app_id, req.to_wxid, img)
        return APIResponse(ok=True, data=result)

    def send_app(self, req: SendAppRequest) -> APIResponse:
        app_id = self._require_app_id()
        result = self._client.post_app_msg(app_id, req.to_wxid, req.xml)
        return APIResponse(ok=True, data=result)

    def send_voice(self, req: SendVoiceRequest) -> APIResponse:
        vurl = req.voice_base64
        if not (isinstance(vurl, str) and vurl.lower().startswith(("http://", "https://"))):
            raise APIValidationError("V3 postVoice 需要 voiceUrl（http/https，silk），请传可访问链接或改用 V1/V2 发送base64")
        app_id = self._require_app_id()
        result = self._client.post_voice(app_id, req.to_wxid, vurl, req.voice_time_ms)
        return APIResponse(ok=True, data=result)

    def share_card(self, req: ShareCardRequest) -> APIResponse:
        app_id = self._require_app_id()
        # GeWe 客户端签名: post_name_card(app_id, to_wxid, nick_name, name_card_wxid)
        result = self._client.post_name_card(app_id, req.to_wxid, req.card_nickname or "", req.card_wxid)
        return APIResponse(ok=True, data=result)

    def revoke_message(self, req: RevokeMessageRequest) -> APIResponse:
        app_id = self._require_app_id()
        result = self._client.revoke_msg(app_id, req.to_user_name, str(req.client_msg_id), str(req.new_msg_id), str(req.create_time))
        return APIResponse(ok=True, data=result)

    # ---- 文件处理 ----
    def upload_image_to_cdn(self, req: UploadImageToCDNRequest) -> APIResponse:
        # V3 平台不提供直接 base64 上传CDN接口；请先将图片上传到可访问的存储并传入其 URL
        _ = req  # 避免未使用参数警告
        return APIResponse(ok=False, message="V3 暂不支持 base64 直传CDN，请传图片URL")

    # ---- 转发（统一接口）----
    def forward_mini_app(self, req: ForwardMiniAppRequest) -> APIResponse:
        app_id = self._require_app_id()
        result = self._client.forward_mini_app(app_id, req.to_wxid, req.xml, req.cover_img_url or "")
        return APIResponse(ok=True, data=result)

    # ---- 好友管理 ----
    def accept_friend(self, req: AcceptFriendRequest) -> APIResponse:
        app_id = self._require_app_id()
        if req.scene is None or req.option is None or not req.v3 or not req.v4:
            return APIResponse(ok=False, message="V3 同意好友需要 scene/option/v3/v4 字段")
        result = self._client.add_contacts(app_id, req.scene, req.option, req.v3, req.v4, req.content or "")
        return APIResponse(ok=True, data=result)

    def get_user_openid(self, req: GetUserOpenidRequest) -> APIResponse:
        # GeWe 平台未提供等价接口，暂不实现
        _ = req  # 避免未使用参数警告
        return APIResponse(ok=False, message="V3 暂不支持获取用户OpenID")

    # ---- 群组管理 ----
    def invite_group_member(self, req: InviteGroupMemberRequest) -> APIResponse:
        app_id = self._require_app_id()
        # 约定：统一模型 chatroom_name 作为 GeWe 的 chatroomId；to_wxids 直接传 wxids
        result = self._client.invite_member(app_id, req.to_wxids, req.chatroom_name, reason="")
        return APIResponse(ok=True, data=result)

    def remove_group_member(self, req: RemoveGroupMemberRequest) -> APIResponse:
        app_id = self._require_app_id()
        result = self._client.remove_member(app_id, req.to_wxids, req.chatroom_name)
        return APIResponse(ok=True, data=result)

    def get_group_info(self, req: GetGroupInfoRequest) -> APIResponse:
        app_id = self._require_app_id()
        result = self._client.get_chatroom_info(app_id, req.qid)
        return APIResponse(ok=True, data=result)

    def get_group_member_detail(self, req: GetGroupMembersRequest) -> APIResponse:
        app_id = self._require_app_id()
        # GeWe 需要 member_wxids 列表；此处用空列表表示查询所有成员详情（如需精确，请扩展模型）
        result = self._client.get_chatroom_member_detail(app_id, req.qid, member_wxids=[])
        return APIResponse(ok=True, data=result)

    # ---- 支付 ----
    def confirm_collection(self, req: ConfirmCollectionRequest) -> APIResponse:
        # 暂未在 GeWe 文档中找到等价支付确认接口
        _ = req  # 避免未使用参数警告
        return APIResponse(ok=False, message="V3 暂不支持确认收款接口")

    # ---- 状态 ----
    def get_online_status(self, req: GetOnlineStatusRequest) -> APIResponse:
        # 优先走官方客户端：check_online(app_id)
        _ = req  # V3 用 appId 判断在线
        app_id = self._require_app_id()
        result = self._client.check_online(app_id)
        return APIResponse(ok=True, data=result)

