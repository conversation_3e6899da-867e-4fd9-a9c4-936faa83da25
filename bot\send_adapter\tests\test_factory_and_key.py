from unittest.mock import patch, MagicMock
from send_adapter.factory import create_adapter
from send_adapter.config import send_settings
from send_adapter.models import SendTextRequest


def test_factory_v1_v2_v3():
    assert create_adapter("v2").__class__.__name__ == "WechatAPIV2Adapter"
    assert create_adapter("v1").__class__.__name__ == "WechatAPIV1Adapter"
    assert create_adapter("v3").__class__.__name__ == "WechatAPIV3Adapter"


def test_api_key_in_params(monkeypatch):
    monkeypatch.setenv("API_VERSION", "v2")
    monkeypatch.setenv("API_KEY", "abc")
    # 重新加载配置
    from importlib import reload
    from send_adapter import config as cfg
    reload(cfg)
    adapter = create_adapter("v2")

    with patch("send_adapter.v2_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok
        adapter.send_text(SendTextRequest(wxid="me", to_wxid="you", content="hi"))
        params = mock_post.call_args[1]["params"]
        assert params["key"] == "abc"

