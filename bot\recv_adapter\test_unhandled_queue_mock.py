#!/usr/bin/env python3
"""
模拟测试未处理消息转发到wxid队列的功能
不依赖真实的RabbitMQ连接
"""
import asyncio
import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock
from framework.core.models import StandardMessage



class MockUnifiedMQPublisher:
    """模拟统一MQ发布器"""

    def __init__(self):
        self.connection = None
        self.channel = None
        self.published_messages = []  # 存储发布的消息

    async def connect(self):
        """模拟连接"""
        print("✅ Mock: Unified MQ publisher connected to RabbitMQ")
        self.connection = MagicMock()
        self.channel = MagicMock()

    async def disconnect(self):
        """模拟断开连接"""
        print("✅ Mock: Unified MQ publisher disconnected from RabbitMQ")

    async def publish_unhandled_message(self, message_data: dict, wxid: str) -> bool:
        """模拟发布未处理消息到wxid队列"""
        try:
            # 模拟队列名称
            queue_name = f"unhandled.{wxid}"

            # 存储发布的消息
            published_message = {
                "queue_name": queue_name,
                "message": message_data,
                "wxid": wxid,
                "timestamp": datetime.now().isoformat()
            }
            self.published_messages.append(published_message)

            print(f"✅ Mock: Successfully published unhandled message to queue {queue_name}")
            print(f"   Message ID: {message_data.get('msg_id')}")
            print(f"   Content: {message_data.get('content')}")
            print(f"   Queue TTL: 1 hour, Message TTL: 1 minute")

            return True

        except Exception as e:
            print(f"❌ Mock: Failed to publish unhandled message to wxid queue {wxid}: {e}")
            return False

    def get_published_messages(self):
        """获取已发布的消息"""
        return self.published_messages

class MockWeChatBotReceiver:
    """模拟微信机器人接收器"""

    def __init__(self):
        self.mq_publisher = MockUnifiedMQPublisher()

    async def _forward_to_wxid_queue(self, message):
        """转发未处理的消息到对应wxid的MQ队列"""
        try:
            # 构建要发布的消息数据
            message_data = message.model_dump(exclude_none=True)

            # 发布到wxid队列
            success = await self.mq_publisher.publish_unhandled_message(
                message_data,
                message.wxid
            )

            if success:
                print(f"✅ Successfully forwarded unhandled message {message.msg_id} to wxid queue {message.wxid}")
            else:
                print(f"❌ Failed to forward unhandled message {message.msg_id} to wxid queue {message.wxid}")

        except Exception as e:
            print(f"❌ Error forwarding message {message.msg_id} to wxid queue: {e}")

async def test_unhandled_message_forwarding():
    """测试未处理消息转发功能"""
    
    print("🧪 Testing unhandled message forwarding (Mock)")
    print("=" * 50)
    
    # 创建模拟接收器
    receiver = MockWeChatBotReceiver()
    
    # 连接到模拟RabbitMQ
    await receiver.mq_publisher.connect()
    
    # 创建多个测试消息
    test_messages = [
        StandardMessage(
            msg_name="AddMsgs",
            msg_id=12345,
            msg_type=1,
            timestamp=int(datetime.now().timestamp()),
            is_self_message=False,
            wxid="wxid_user_001",
            uuid="test_uuid_1",
            from_user_name="test_user_1",
            to_user_name="bot",
            content="这是第一条测试未处理消息",
            push_content="",
            msg_source="",
            ver=1
        ),
        StandardMessage(
            msg_name="AddMsgs",
            msg_id=12346,
            msg_type=1,
            timestamp=int(datetime.now().timestamp()),
            is_self_message=False,
            wxid="wxid_user_002",
            uuid="test_uuid_2",
            from_user_name="test_user_2",
            to_user_name="bot",
            content="这是第二条测试未处理消息",
            push_content="",
            msg_source="",
            ver=1
        ),
        StandardMessage(
            msg_name="AddMsgs",
            msg_id=12347,
            msg_type=1,
            timestamp=int(datetime.now().timestamp()),
            is_self_message=False,
            wxid="wxid_user_001",  # 同一个用户的第二条消息
            uuid="test_uuid_3",
            from_user_name="test_user_1",
            to_user_name="bot",
            content="同一用户的第二条消息",
            push_content="",
            msg_source="",
            ver=1
        )
    ]
    
    print(f"\n📨 Processing {len(test_messages)} test messages...")
    
    # 处理每条消息
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- Processing Message {i} ---")
        print(f"Message ID: {message.msg_id}")
        print(f"WXID: {message.wxid}")
        print(f"Content: {message.content}")
        
        # 转发到wxid队列
        await receiver._forward_to_wxid_queue(message)
    
    # 显示发布的消息统计
    published_messages = receiver.mq_publisher.get_published_messages()
    
    print(f"\n📊 Summary:")
    print(f"Total messages processed: {len(test_messages)}")
    print(f"Total messages published: {len(published_messages)}")
    
    # 按队列分组统计
    queue_stats = {}
    for msg in published_messages:
        queue_name = msg["queue_name"]
        if queue_name not in queue_stats:
            queue_stats[queue_name] = 0
        queue_stats[queue_name] += 1
    
    print(f"\n📋 Queue Statistics:")
    for queue_name, count in queue_stats.items():
        print(f"  {queue_name}: {count} messages")
    
    print(f"\n📝 Published Messages Details:")
    for i, msg in enumerate(published_messages, 1):
        print(f"  {i}. Queue: {msg['queue_name']}")
        print(f"     Message ID: {msg['message']['msg_id']}")
        print(f"     Content: {msg['message']['content']}")
        print(f"     Timestamp: {msg['timestamp']}")
    
    # 断开连接
    await receiver.mq_publisher.disconnect()
    
    print(f"\n✅ Mock test completed successfully!")

async def test_queue_configuration():
    """测试队列配置"""
    
    print("\n🔧 Testing queue configuration...")
    
    # 模拟队列配置
    queue_config = {
        "x-message-ttl": 60000,  # 消息TTL 1分钟
        "x-expires": 3600000,    # 队列TTL 1小时
    }
    
    print(f"Queue Configuration:")
    print(f"  Message TTL: {queue_config['x-message-ttl']} ms (1 minute)")
    print(f"  Queue TTL: {queue_config['x-expires']} ms (1 hour)")
    print(f"  Durable: True")
    print(f"  Delivery Mode: Persistent")
    
    # 验证TTL计算
    message_ttl_minutes = queue_config['x-message-ttl'] / 1000 / 60
    queue_ttl_hours = queue_config['x-expires'] / 1000 / 60 / 60
    
    print(f"\nValidation:")
    print(f"  ✅ Message TTL: {message_ttl_minutes} minutes")
    print(f"  ✅ Queue TTL: {queue_ttl_hours} hours")

async def main():
    """主测试函数"""
    print("🧪 Mock Testing: Unhandled Message Forwarding to WXID Queues")
    print("=" * 70)
    
    await test_unhandled_message_forwarding()
    await test_queue_configuration()
    
    print("\n" + "=" * 70)
    print("✅ All mock tests completed successfully!")
    print("\n💡 Next steps:")
    print("   1. Configure RabbitMQ connection settings")
    print("   2. Test with real RabbitMQ server")
    print("   3. Monitor queue creation and message TTL")

if __name__ == "__main__":
    asyncio.run(main())
