"""
组管理器
统一管理多个组的适配器，提供全局的消息发送和管理功能
"""
import time
import threading
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

from group_adapter import GroupAdapter
from database import DatabaseManager
from send_adapter.models import APIResponse


class GroupManager:
    """组管理器，统一管理多个组的适配器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化组管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.group_adapters: Dict[str, GroupAdapter] = {}
        self.lock = threading.RLock()
        self.health_check_interval = 60  # 健康检查间隔（秒）
        self.last_health_check = 0
        
        logger.info("组管理器初始化完成")
    
    def get_or_create_group_adapter(self, group_name: str) -> Optional[GroupAdapter]:
        """
        获取或创建组适配器
        
        Args:
            group_name: 组名
            
        Returns:
            组适配器实例，如果创建失败返回None
        """
        with self.lock:
            # 如果已存在，直接返回
            if group_name in self.group_adapters:
                return self.group_adapters[group_name]
            
            # 创建新的组适配器
            try:
                group_adapter = GroupAdapter(group_name, self.db_manager)
                
                # 检查是否有可用账号
                if group_adapter.get_available_accounts_count() == 0:
                    logger.warning(f"组 {group_name} 没有可用的账号")
                    return None
                
                self.group_adapters[group_name] = group_adapter
                logger.info(f"创建组适配器 {group_name} 成功")
                return group_adapter
                
            except Exception as e:
                logger.error(f"创建组适配器 {group_name} 失败: {e}")
                return None
    
    def send_message_to_group(self, group_name: str, method_name: str, *args, **kwargs) -> APIResponse:
        """
        向指定组发送消息
        
        Args:
            group_name: 组名
            method_name: 方法名
            *args, **kwargs: 方法参数
            
        Returns:
            API响应
        """
        group_adapter = self.get_or_create_group_adapter(group_name)
        
        if not group_adapter:
            return APIResponse(ok=False, message=f"组 {group_name} 不可用")
        
        return group_adapter.send_message(method_name, *args, **kwargs)
    
    def send_text_to_group(self, group_name: str, request) -> APIResponse:
        """向指定组发送文本消息"""
        return self.send_message_to_group(group_name, 'send_text', request)
    
    def send_image_to_group(self, group_name: str, request) -> APIResponse:
        """向指定组发送图片消息"""
        return self.send_message_to_group(group_name, 'send_image', request)
    
    def broadcast_message(self, groups: List[str], method_name: str, *args, **kwargs) -> Dict[str, APIResponse]:
        """
        向多个组广播消息
        
        Args:
            groups: 组名列表
            method_name: 方法名
            *args, **kwargs: 方法参数
            
        Returns:
            每个组的响应结果
        """
        results = {}
        
        # 使用线程池并发发送
        with ThreadPoolExecutor(max_workers=min(len(groups), 10)) as executor:
            # 提交任务
            future_to_group = {
                executor.submit(self.send_message_to_group, group, method_name, *args, **kwargs): group
                for group in groups
            }
            
            # 收集结果
            for future in as_completed(future_to_group):
                group = future_to_group[future]
                try:
                    response = future.result(timeout=30)
                    results[group] = response
                except Exception as e:
                    logger.error(f"向组 {group} 发送消息异常: {e}")
                    results[group] = APIResponse(ok=False, message=str(e))
        
        return results
    
    def get_all_groups(self) -> List[str]:
        """获取所有可用的组"""
        try:
            return self.db_manager.get_all_groups()
        except Exception as e:
            logger.error(f"获取组列表失败: {e}")
            return []
    
    def get_group_stats(self, group_name: str) -> Optional[Dict[str, Any]]:
        """获取指定组的统计信息"""
        group_adapter = self.group_adapters.get(group_name)
        if group_adapter:
            return group_adapter.get_stats()
        return None
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有组的统计信息"""
        stats = {
            'total_groups': len(self.group_adapters),
            'healthy_groups': 0,
            'total_accounts': 0,
            'available_accounts': 0,
            'groups': {}
        }
        
        for group_name, group_adapter in self.group_adapters.items():
            group_stats = group_adapter.get_stats()
            stats['groups'][group_name] = group_stats
            
            if group_adapter.is_healthy():
                stats['healthy_groups'] += 1
            
            stats['total_accounts'] += group_stats.get('total_accounts', 0)
            stats['available_accounts'] += group_stats.get('available_accounts', 0)
        
        return stats
    
    def health_check(self) -> Dict[str, bool]:
        """
        执行健康检查
        
        Returns:
            每个组的健康状态
        """
        health_status = {}
        
        for group_name, group_adapter in self.group_adapters.items():
            try:
                is_healthy = group_adapter.is_healthy()
                health_status[group_name] = is_healthy
                
                if not is_healthy:
                    logger.warning(f"组 {group_name} 健康检查失败，尝试刷新配置")
                    group_adapter.force_refresh()
                    
            except Exception as e:
                logger.error(f"组 {group_name} 健康检查异常: {e}")
                health_status[group_name] = False
        
        self.last_health_check = time.time()
        return health_status
    
    def auto_health_check(self):
        """自动健康检查（如果需要）"""
        if time.time() - self.last_health_check > self.health_check_interval:
            logger.info("执行自动健康检查")
            self.health_check()
    
    def refresh_group(self, group_name: str) -> bool:
        """
        刷新指定组的配置
        
        Args:
            group_name: 组名
            
        Returns:
            是否刷新成功
        """
        group_adapter = self.group_adapters.get(group_name)
        if group_adapter:
            try:
                group_adapter.force_refresh()
                logger.info(f"刷新组 {group_name} 配置成功")
                return True
            except Exception as e:
                logger.error(f"刷新组 {group_name} 配置失败: {e}")
                return False
        else:
            logger.warning(f"组 {group_name} 不存在")
            return False
    
    def refresh_all_groups(self) -> Dict[str, bool]:
        """
        刷新所有组的配置
        
        Returns:
            每个组的刷新结果
        """
        results = {}
        for group_name in self.group_adapters.keys():
            results[group_name] = self.refresh_group(group_name)
        return results
    
    def remove_group(self, group_name: str) -> bool:
        """
        移除指定组
        
        Args:
            group_name: 组名
            
        Returns:
            是否移除成功
        """
        with self.lock:
            if group_name in self.group_adapters:
                del self.group_adapters[group_name]
                logger.info(f"移除组 {group_name} 成功")
                return True
            else:
                logger.warning(f"组 {group_name} 不存在")
                return False
    
    def get_group_count(self) -> int:
        """获取当前管理的组数量"""
        return len(self.group_adapters)
    
    def is_group_available(self, group_name: str) -> bool:
        """检查指定组是否可用"""
        group_adapter = self.group_adapters.get(group_name)
        return group_adapter is not None and group_adapter.is_healthy()
    
    def get_available_groups(self) -> List[str]:
        """获取所有可用的组名"""
        return [
            group_name for group_name, group_adapter in self.group_adapters.items()
            if group_adapter.is_healthy()
        ]
    
    def shutdown(self):
        """关闭组管理器"""
        logger.info("正在关闭组管理器...")
        with self.lock:
            self.group_adapters.clear()
        logger.info("组管理器已关闭")
