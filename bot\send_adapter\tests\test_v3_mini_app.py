from unittest.mock import patch, MagicMock
from send_adapter.factory import create_adapter
from send_adapter.models import SendMiniAppV3Re<PERSON>


def test_send_mini_app_v3_headers_and_payload(monkeypatch):
    monkeypatch.setenv("API_VERSION", "v3")
    monkeypatch.setenv("GEWE_TOKEN", "token123")
    monkeypatch.setenv("GEWE_APPID", "app123")

    from importlib import reload
    from send_adapter import config as cfg
    reload(cfg)

    adapter = create_adapter("v3")

    with patch("send_adapter.v3_adapter.requests.post") as mock_post:
        ok = MagicMock(); ok.status_code = 200; ok.json.return_value = {}
        mock_post.return_value = ok

        req = SendMiniAppV3Request(
            app_id="app123", to_wxid="34757816141@chatroom", mini_app_id="wx1...",
            user_name="gh_xxx@app", title="t", cover_img_url="https://img",
            page_path="pages/home", display_name="展示名"
        )
        adapter.send_mini_app_v3(req)

        # 路径
        assert mock_post.call_args[0][0].endswith("/message/postMiniApp")
        # 头注入
        headers = mock_post.call_args[1]["headers"]
        assert headers["X-GEWE-TOKEN"] == "token123"
        # 负载字段大小写
        payload = mock_post.call_args[1]["json"]
        assert payload["toWxid"] == "34757816141@chatroom"
        assert payload["userName"].endswith("@app")

