#!/usr/bin/env python3
"""
微信API多账号管理应用程序演示
展示完整的功能和使用方法
"""
import os
import sys
import time
import threading
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from group_adapter import GroupAdapter
from group_manager import GroupManager
from send_adapter.models import SendTextRequest, SendImageRequest


def setup_logger():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def show_banner():
    """显示演示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              微信API多账号管理应用程序演示                   ║
║                                                              ║
║  🏗️ 基于现有适配器框架构建的完整应用程序                     ║
║  📊 支持多组账号管理和故障转移                               ║
║  🔄 集成消息队列和数据库管理                                 ║
║  📱 实时监控和统计功能                                       ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def demo_database_integration():
    """演示数据库集成功能"""
    logger.info("🗄️ 演示数据库集成功能")
    logger.info("-" * 50)
    
    # 创建数据库管理器（使用模拟数据）
    db_manager = DatabaseManager(use_mock=True)
    
    # 获取所有组
    groups = db_manager.get_all_groups()
    logger.info(f"📋 发现组: {groups}")
    
    # 获取YBA组的配置
    yba_agents = db_manager.get_agents_by_group("YBA")
    logger.info(f"👥 YBA组账号数: {len(yba_agents)}")
    
    for agent in yba_agents:
        logger.info(f"  - {agent.name} (ID: {agent.id}, API: {agent.api_version})")
        logger.info(f"    状态: {'✅ 激活' if agent.wechat_is_active else '❌ 未激活'}")
        logger.info(f"    登录: {'🟢 在线' if agent.wechat_login_status else '🔴 离线'}")
    
    return db_manager


def demo_group_adapter(db_manager):
    """演示组适配器功能"""
    logger.info("\n🔧 演示组适配器功能")
    logger.info("-" * 50)
    
    # 创建YBA组适配器
    group_adapter = GroupAdapter("YBA", db_manager)
    
    # 获取统计信息
    stats = group_adapter.get_stats()
    logger.info(f"📊 YBA组统计:")
    logger.info(f"  - 总账号: {stats['total_accounts']}")
    logger.info(f"  - 可用账号: {stats['available_accounts']}")
    logger.info(f"  - 健康状态: {'✅ 健康' if group_adapter.is_healthy() else '❌ 不健康'}")
    
    # 显示账号优先级
    logger.info("🎯 账号优先级排序:")
    for account_info in stats.get('accounts', []):
        status_icon = "🟢" if account_info['status'] == "active" else "🔴"
        logger.info(f"  {account_info['priority']}. {account_info['account_id']} "
                   f"({account_info['api_version']}) {status_icon}")
    
    return group_adapter


def demo_message_sending(group_adapter):
    """演示消息发送功能"""
    logger.info("\n📤 演示消息发送功能")
    logger.info("-" * 50)
    
    # 发送文本消息
    text_request = SendTextRequest(
        wxid="auto_select",
        to_wxid="YBA-********",
        content="🎯 演示：多账号故障转移测试消息"
    )
    
    logger.info("📝 发送文本消息...")
    response = group_adapter.send_text(text_request)
    
    if response.ok:
        logger.info("✅ 文本消息发送成功!")
        
        # 显示使用的账号
        stats = group_adapter.get_stats()
        current_account = stats.get('current_account')
        if current_account:
            logger.info(f"🎯 使用账号: {current_account}")
    else:
        logger.error(f"❌ 文本消息发送失败: {response.message}")
    
    # 发送图片消息
    image_request = SendImageRequest(
        wxid="auto_select",
        to_wxid="YBA-********",
        image_base64="https://picsum.photos/400/300"
    )
    
    logger.info("🖼️ 发送图片消息...")
    response = group_adapter.send_image(image_request)
    
    if response.ok:
        logger.info("✅ 图片消息发送成功!")
    else:
        logger.error(f"❌ 图片消息发送失败: {response.message}")


def demo_failover_mechanism(group_adapter):
    """演示故障转移机制"""
    logger.info("\n🔄 演示故障转移机制")
    logger.info("-" * 50)
    
    # 获取当前账号
    stats = group_adapter.get_stats()
    current_account_id = stats.get('current_account')
    
    if current_account_id and group_adapter.multi_adapter:
        current_account = group_adapter.multi_adapter.current_account
        
        if current_account:
            logger.info(f"🎯 当前账号: {current_account.account_id}")
            logger.info(f"📊 当前成功率: {current_account.get_success_rate():.2%}")
            
            # 模拟账号故障
            logger.info("⚠️ 模拟账号故障...")
            original_error_count = current_account.error_count
            
            # 标记多次错误
            for i in range(6):
                current_account.mark_error()
            
            logger.info(f"📈 错误次数: {original_error_count} → {current_account.error_count}")
            logger.info(f"📊 账号状态: {current_account.status}")
            
            # 发送测试消息，应该会触发故障转移
            test_request = SendTextRequest(
                wxid="auto_select",
                to_wxid="YBA-********",
                content="🔄 故障转移测试 - 应该切换到备用账号"
            )
            
            logger.info("🔄 触发故障转移...")
            response = group_adapter.send_text(test_request)
            
            # 检查是否切换了账号
            new_stats = group_adapter.get_stats()
            new_current_account = new_stats.get('current_account')
            
            if new_current_account != current_account_id:
                logger.info(f"✅ 故障转移成功! {current_account_id} → {new_current_account}")
            else:
                logger.info("ℹ️ 故障转移逻辑已执行（可能没有其他可用账号）")


def demo_group_manager(db_manager):
    """演示组管理器功能"""
    logger.info("\n🏗️ 演示组管理器功能")
    logger.info("-" * 50)
    
    # 创建组管理器
    group_manager = GroupManager(db_manager)
    
    # 创建多个组
    yba_adapter = group_manager.get_or_create_group_adapter("YBA")
    test_adapter = group_manager.get_or_create_group_adapter("TEST")
    
    logger.info(f"📊 组管理器状态:")
    logger.info(f"  - YBA组: {'✅ 可用' if yba_adapter else '❌ 不可用'}")
    logger.info(f"  - TEST组: {'✅ 可用' if test_adapter else '❌ 不可用'}")
    
    # 获取全局统计
    all_stats = group_manager.get_all_stats()
    logger.info(f"📈 全局统计:")
    logger.info(f"  - 总组数: {all_stats['total_groups']}")
    logger.info(f"  - 健康组数: {all_stats['healthy_groups']}")
    logger.info(f"  - 总账号数: {all_stats['total_accounts']}")
    logger.info(f"  - 可用账号数: {all_stats['available_accounts']}")
    
    # 测试跨组消息发送
    test_request = SendTextRequest(
        wxid="auto_select",
        to_wxid="YBA-********",
        content="🏗️ 通过组管理器发送的消息"
    )
    
    logger.info("📤 通过组管理器发送消息...")
    response = group_manager.send_text_to_group("YBA", test_request)
    
    if response.ok:
        logger.info("✅ 组管理器消息发送成功!")
    else:
        logger.error(f"❌ 组管理器消息发送失败: {response.message}")
    
    return group_manager


def demo_health_monitoring(group_manager):
    """演示健康监控功能"""
    logger.info("\n🏥 演示健康监控功能")
    logger.info("-" * 50)
    
    # 执行健康检查
    health_status = group_manager.health_check()
    
    logger.info("🔍 健康检查结果:")
    for group_name, is_healthy in health_status.items():
        status_icon = "✅" if is_healthy else "❌"
        logger.info(f"  - {group_name}: {status_icon} {'健康' if is_healthy else '不健康'}")
    
    # 获取详细统计
    for group_name in health_status.keys():
        group_stats = group_manager.get_group_stats(group_name)
        if group_stats:
            logger.info(f"📊 {group_name}组详细统计:")
            logger.info(f"  - 可用账号: {group_stats['available_accounts']}/{group_stats['total_accounts']}")
            
            for account_info in group_stats.get('accounts', []):
                total_requests = account_info.get('total_requests', 0)
                success_rate = account_info.get('success_rate', 0)
                logger.info(f"    • {account_info['account_id']}: "
                           f"请求{total_requests}次, 成功率{success_rate:.1%}")


def demo_concurrent_operations(group_adapter):
    """演示并发操作"""
    logger.info("\n⚡ 演示并发操作")
    logger.info("-" * 50)
    
    def send_concurrent_message(message_id):
        request = SendTextRequest(
            wxid="auto_select",
            to_wxid="YBA-********",
            content=f"⚡ 并发消息 #{message_id}"
        )
        
        response = group_adapter.send_text(request)
        return response.ok
    
    # 并发发送消息
    logger.info("🚀 启动并发发送测试...")
    
    threads = []
    results = []
    
    for i in range(3):
        def worker(msg_id=i):
            success = send_concurrent_message(msg_id)
            results.append(success)
            logger.info(f"{'✅' if success else '❌'} 并发消息 #{msg_id}")
        
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    success_count = sum(results)
    logger.info(f"📊 并发测试结果: {success_count}/{len(results)} 条消息成功")


def demo_configuration_management():
    """演示配置管理"""
    logger.info("\n⚙️ 演示配置管理")
    logger.info("-" * 50)
    
    from config import get_config
    
    config = get_config()
    
    logger.info("📋 当前配置:")
    logger.info(f"  - 数据库: {config.database.db_host}:{config.database.db_port}")
    logger.info(f"  - 使用模拟数据库: {config.should_use_mock_db()}")
    logger.info(f"  - 消息队列: {config.message_queue.mq_host}:{config.message_queue.mq_port}")
    logger.info(f"  - 使用模拟队列: {config.should_use_mock_mq()}")
    logger.info(f"  - 日志级别: {config.app.log_level}")
    logger.info(f"  - 健康检查间隔: {config.app.health_check_interval}秒")


def main():
    """主演示函数"""
    setup_logger()
    show_banner()
    
    logger.info("🚀 开始微信API多账号管理应用程序演示")
    logger.info("=" * 60)
    
    try:
        # 1. 数据库集成演示
        db_manager = demo_database_integration()
        
        # 2. 组适配器演示
        group_adapter = demo_group_adapter(db_manager)
        
        # 3. 消息发送演示
        demo_message_sending(group_adapter)
        
        # 4. 故障转移演示
        demo_failover_mechanism(group_adapter)
        
        # 5. 组管理器演示
        group_manager = demo_group_manager(db_manager)
        
        # 6. 健康监控演示
        demo_health_monitoring(group_manager)
        
        # 7. 并发操作演示
        demo_concurrent_operations(group_adapter)
        
        # 8. 配置管理演示
        demo_configuration_management()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 演示完成!")
        
        logger.info("\n💡 关键特性总结:")
        logger.info("✅ 多账号故障转移 - 自动选择最佳账号")
        logger.info("✅ 组级别管理 - 独立的组配置和监控")
        logger.info("✅ 数据库集成 - 动态加载账号配置")
        logger.info("✅ 消息队列支持 - 异步消息处理")
        logger.info("✅ 健康监控 - 实时状态检查")
        logger.info("✅ 并发处理 - 线程安全的消息发送")
        logger.info("✅ 配置管理 - 灵活的环境配置")
        
        logger.info("\n🚀 启动应用程序:")
        logger.info("  python app.py --group YBA --mock-db --mock-mq")
        logger.info("  python start_yba.py")
        
        logger.info("\n🧪 运行测试:")
        logger.info("  python test_yba_group.py")
        
    except Exception as e:
        logger.error(f"❌ 演示过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        if 'db_manager' in locals():
            db_manager.close()


if __name__ == "__main__":
    main()
