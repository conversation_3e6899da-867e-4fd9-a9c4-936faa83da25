# 微信API适配器使用示例

本目录包含使用微信API适配器的各种示例代码，展示从传统配置方式到新的账号对象管理方式的演进。

## 🏗️ 新架构特性

### 账号对象管理
- 统一的账号信息管理
- 支持V1/V2/V3多版本
- 内置状态和统计功能
- 优先级和故障转移支持

### 多账号故障转移
- 自动账号选择
- 智能故障检测
- 无缝切换机制
- 详细的监控统计

## 📁 文件说明

### 核心示例

#### 1. `account_based_example.py` - 账号对象完整演示
展示新架构的所有功能：
- 单账号适配器使用
- 多账号故障转移
- 账号管理器功能
- 故障转移场景模拟

#### 2. `migration_example.py` - 新旧方式对比
展示如何从传统方式迁移到新架构：
- 旧方式：直接传入配置
- 新方式：使用账号对象
- 多账号故障转移演示
- 详细的迁移指南

### 专项测试

#### 3. `test_image_send.py` - 图片发送测试
专门测试V3接口的图片发送功能：
- 多种图片URL测试
- 自定义图片URL支持
- 详细的错误分析

#### 4. `test_app_message.py` - 应用消息测试
测试各种应用消息类型：
- 链接消息
- 纯文本消息
- 自定义XML支持

## 🚀 快速开始

### 传统方式（向后兼容）
```python
from send_adapter.factory import create_adapter
from send_adapter.models import SendTextRequest

# 直接传入配置
adapter = create_adapter("v3",
    v3_base_url="http://api.geweapi.com/gewe/v2/api",
    gewe_token="your_token",
    gewe_appid="your_appid"
)

request = SendTextRequest(
    wxid="your_wxid",
    to_wxid="target_wxid",
    content="Hello World!"
)

response = adapter.send_text(request)
```

### 新方式：账号对象（推荐）
```python
from send_adapter.factory import create_adapter
from send_adapter.account import WechatAccount
from send_adapter.models import SendTextRequest

# 创建账号对象
account = WechatAccount.create_v3_account(
    account_id="my_account",
    account_name="我的V3账号",
    wxid="your_wxid",
    base_url="http://api.geweapi.com/gewe/v2/api",
    gewe_token="your_token",
    gewe_appid="your_appid"
)

# 使用账号对象创建适配器
adapter = create_adapter(account)

request = SendTextRequest(
    wxid=account.wxid,
    to_wxid="target_wxid",
    content="Hello from account-based adapter!"
)

response = adapter.send_text(request)

# 自动统计成功率
if response.ok:
    account.mark_success()
else:
    account.mark_error()
```

### 多账号故障转移
```python
from send_adapter.factory import create_multi_account_adapter
from send_adapter.account import WechatAccount

# 创建多个账号
accounts = [
    WechatAccount.create_v3_account(
        account_id="main_v3",
        account_name="主账号",
        wxid="main_wxid",
        base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="main_token",
        gewe_appid="main_appid",
        priority=1  # 最高优先级
    ),
    WechatAccount.create_v1_account(
        account_id="backup_v1",
        account_name="备用账号",
        wxid="backup_wxid",
        base_url="http://8.133.252.208:8080",
        api_key="backup_key",
        priority=2  # 较低优先级
    )
]

# 创建多账号适配器
multi_adapter = create_multi_account_adapter(accounts)

# 自动选择最佳账号发送，支持故障转移
response = multi_adapter.send_text(request)
```

## 🔧 配置说明

### 账号对象字段

#### 基础信息
- `account_id`: 账号唯一标识
- `account_name`: 账号名称/描述
- `wxid`: 微信ID
- `api_version`: API版本 (v1/v2/v3)

#### 连接配置
- `base_url`: API基础URL
- `timeout`: 请求超时时间（默认30秒）
- `max_retries`: 最大重试次数（默认3次）
- `backoff_factor`: 重试退避因子（默认1.0）

#### 认证配置
- V1/V2: `api_key`
- V3: `gewe_token` + `gewe_appid`

#### 管理配置
- `priority`: 优先级（数字越小优先级越高）
- `status`: 账号状态 (active/inactive/disabled/error)

### 环境变量（可选）
如果不使用账号对象，仍可通过环境变量配置：
```bash
# V3配置
export GEWE_TOKEN=your_token
export GEWE_APPID=your_appid
export V3_BASE_URL=http://api.geweapi.com/gewe/v2/api

# V1配置
export API_KEY=your_v1_key
export V1_BASE_URL=http://8.133.252.208:8080

# V2配置
export API_KEY=your_v2_key
export V2_BASE_URL=http://8.133.252.208:8060/api
```

## 📊 监控和统计

### 账号统计
```python
# 获取账号成功率
success_rate = account.get_success_rate()

# 获取请求统计
total_requests = account.success_count + account.error_count
print(f"成功: {account.success_count}, 失败: {account.error_count}")
print(f"成功率: {success_rate:.2%}")
```

### 多账号统计
```python
# 获取多账号适配器统计
stats = multi_adapter.get_account_stats()
print(f"总账号数: {stats['total_accounts']}")
print(f"可用账号数: {stats['available_accounts']}")
print(f"当前使用: {stats['current_account']}")
```

## 🔄 故障转移机制

### 自动切换条件
1. 当前账号状态变为不可用
2. 发现更高优先级的可用账号
3. 当前账号连续失败次数过多

### 切换策略
1. 按优先级排序
2. 考虑成功率
3. 避免频繁切换（最小间隔60秒）

### 错误处理
- 4xx错误：不进行故障转移
- 5xx错误：尝试故障转移
- 网络错误：尝试故障转移

## 🧪 运行示例

```bash
# 进入示例目录
cd bot/send_adapter/examples

# 运行完整演示
python account_based_example.py

# 运行迁移对比
python migration_example.py

# 测试图片发送
python test_image_send.py

# 测试应用消息
python test_app_message.py
```

## 💡 最佳实践

### 生产环境建议
1. **配置多个账号**：至少配置2-3个不同版本的账号
2. **设置合理优先级**：主账号优先级1，备用账号优先级2、3等
3. **定期监控**：监控账号状态和成功率
4. **日志记录**：启用详细日志记录故障转移过程

### 开发环境建议
1. **使用单账号**：开发时使用单个稳定账号
2. **启用调试日志**：设置日志级别为DEBUG
3. **测试故障转移**：定期测试多账号故障转移功能

### 迁移建议
1. **渐进式迁移**：先在新功能中使用账号对象
2. **保持兼容**：旧代码可以继续使用传统方式
3. **统一管理**：逐步将所有配置迁移到账号对象

## 🔍 故障排除

### 常见问题

1. **账号不可用**
   - 检查账号状态：`account.status`
   - 检查认证信息：token、appid等
   - 检查网络连接

2. **故障转移不工作**
   - 确认有多个可用账号
   - 检查账号优先级设置
   - 查看错误日志

3. **成功率异常**
   - 检查API配置是否正确
   - 确认目标用户存在
   - 查看详细错误信息

### 调试技巧
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看账号状态
print(f"账号状态: {account.status}")
print(f"成功率: {account.get_success_rate():.2%}")

# 查看多账号统计
stats = multi_adapter.get_account_stats()
for acc_info in stats['accounts']:
    print(f"账号: {acc_info['account_id']}, 状态: {acc_info['status']}")
```
