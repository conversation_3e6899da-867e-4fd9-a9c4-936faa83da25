#!/usr/bin/env python3
"""
测试 V3 接口发送应用消息的示例
提供多种有效的应用消息 XML 模板
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import SendAppRequest


def create_v3_adapter():
    """创建配置好的 V3 适配器"""
    return create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"
    )


def get_link_message_xml():
    """获取链接消息的 XML"""
    return """<?xml version="1.0"?>
<msg>
    <appmsg appid="" sdkver="0">
        <title>测试链接消息</title>
        <des>这是一个测试链接，点击查看详情</des>
        <action>view</action>
        <type>5</type>
        <showtype>0</showtype>
        <content></content>
        <url>https://www.baidu.com</url>
        <lowurl></lowurl>
        <dataurl></dataurl>
        <lowdataurl></lowdataurl>
        <contentattr>0</contentattr>
        <streamvideo>
            <streamvideourl></streamvideourl>
            <streamvideototaltime>0</streamvideototaltime>
            <streamvideotitle></streamvideotitle>
            <streamvideowording></streamvideowording>
            <streamvideoweburl></streamvideoweburl>
            <streamvideothumburl></streamvideothumburl>
            <streamvideoaduxinfo></streamvideoaduxinfo>
            <streamvideopublishid></streamvideopublishid>
        </streamvideo>
        <canvasPageItem>
            <canvasPageXml><![CDATA[]]></canvasPageXml>
        </canvasPageItem>
        <appattach>
            <totallen>0</totallen>
            <attachid></attachid>
            <emoticonmd5></emoticonmd5>
            <fileext></fileext>
            <cdnthumburl></cdnthumburl>
            <cdnthumbmd5></cdnthumbmd5>
            <cdnthumblength>0</cdnthumblength>
            <cdnthumbwidth>0</cdnthumbwidth>
            <cdnthumbheight>0</cdnthumbheight>
            <cdnthumbaeskey></cdnthumbaeskey>
            <aeskey></aeskey>
            <encryver>0</encryver>
            <filekey></filekey>
        </appattach>
        <extinfo></extinfo>
        <sourceusername></sourceusername>
        <sourcedisplayname></sourcedisplayname>
        <thumburl></thumburl>
        <md5></md5>
        <statextstr></statextstr>
    </appmsg>
    <fromusername></fromusername>
    <scene>0</scene>
    <appinfo>
        <version>1</version>
        <appname></appname>
    </appinfo>
    <commenturl></commenturl>
</msg>"""


def get_simple_link_xml():
    """获取简化的链接消息 XML"""
    return """<msg>
    <appmsg appid="" sdkver="0">
        <title>简单测试链接</title>
        <des>这是一个简化的测试链接</des>
        <action>view</action>
        <type>5</type>
        <url>https://www.github.com</url>
        <thumburl></thumburl>
    </appmsg>
</msg>"""


def get_text_message_xml():
    """获取纯文本应用消息 XML"""
    return """<msg>
    <appmsg appid="" sdkver="0">
        <title>纯文本消息</title>
        <des>这是一个纯文本的应用消息，不包含链接</des>
        <action></action>
        <type>1</type>
        <url></url>
    </appmsg>
</msg>"""


def test_app_message(xml_content, message_name):
    """测试发送应用消息"""
    print(f"\n📱 测试 {message_name}...")
    
    adapter = create_v3_adapter()
    
    request = SendAppRequest(
        wxid="wxid_yba19990312",
        to_wxid="YBA-19990312",
        xml=xml_content.strip()
    )
    
    try:
        print("   📤 发送中...")
        response = adapter.send_app(request)
        
        if response.ok:
            print("   ✅ 发送成功!")
            if response.data:
                msg_id = response.data.get('newMsgId', 'N/A')
                print(f"   📋 消息ID: {msg_id}")
            return True
        else:
            print(f"   ❌ 发送失败: {response.message}")
            return False
            
    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 异常: {error_msg}")
        
        # 解析错误信息
        if '"ret":-2' in error_msg:
            print("   💡 错误代码 -2: XML 格式或内容不符合要求")
        elif '"ret":-1' in error_msg:
            print("   💡 错误代码 -1: 一般性错误，可能是权限或参数问题")
        elif '"ret":500' in error_msg:
            print("   💡 服务器错误: 可能是临时性问题")
            
        return False


def test_custom_xml():
    """测试自定义 XML"""
    print("\n🎯 测试自定义应用消息 XML")
    print("-" * 40)
    
    print("请输入应用消息 XML (输入 'skip' 跳过):")
    print("提示: 可以从微信开发者工具或抓包工具获取真实的 XML")
    
    lines = []
    while True:
        line = input()
        if line.strip().lower() == 'skip':
            print("⏭️ 跳过自定义 XML 测试")
            return False
        if line.strip() == '':
            break
        lines.append(line)
    
    if not lines:
        print("❌ 未输入 XML 内容")
        return False
    
    custom_xml = '\n'.join(lines)
    return test_app_message(custom_xml, "自定义 XML")


def main():
    """主函数"""
    print("🚀 V3 接口应用消息发送测试")
    print("=" * 50)
    
    # 测试不同类型的应用消息
    test_cases = [
        (get_simple_link_xml(), "简化链接消息"),
        (get_text_message_xml(), "纯文本消息"),
        (get_link_message_xml(), "完整链接消息"),
    ]
    
    results = []
    
    for xml_content, name in test_cases:
        success = test_app_message(xml_content, name)
        results.append((name, success))
    
    # 测试自定义 XML
    custom_success = test_custom_xml()
    if custom_success is not False:
        results.append(("自定义 XML", custom_success))
    
    # 显示测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    print(f"\n总计: {success_count}/{len(results)} 条应用消息发送成功")
    
    print("\n💡 应用消息发送要点:")
    print("1. XML 格式必须正确，建议使用真实的微信应用消息 XML")
    print("2. type 字段决定消息类型:")
    print("   - type=1: 纯文本消息")
    print("   - type=5: 链接消息")
    print("   - type=33: 小程序消息")
    print("3. 如果有 URL，确保链接可以正常访问")
    print("4. appid 可以为空，但某些类型消息可能需要有效的 appid")
    print("5. 避免使用可能被微信限制的内容或链接")
    
    if success_count == 0:
        print("\n⚠️ 所有应用消息都发送失败，可能的原因:")
        print("   - XML 格式不符合微信规范")
        print("   - 消息内容被微信安全策略拦截")
        print("   - 账号权限不足")
        print("   - 建议使用从真实微信消息中提取的 XML")


if __name__ == "__main__":
    main()
