#!/usr/bin/env python3
"""
测试 V3 接口发送图片的示例
使用真实可访问的图片 URL 进行测试
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from send_adapter.factory import create_adapter
from send_adapter.models import SendImageRequest


def test_image_urls():
    """测试不同的图片 URL"""
    
    # 创建 V3 适配器
    adapter = create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"
    )
    
    # 测试用的图片 URL 列表
    test_images = [
        {
            "name": "随机图片服务",
            "url": "https://picsum.photos/300/200",
            "description": "Lo<PERSON> Picsum 随机图片服务"
        },
        {
            "name": "GitHub 头像",
            "url": "https://avatars.githubusercontent.com/u/1?v=4",
            "description": "GitHub 用户头像"
        },
        {
            "name": "微信公众号图片",
            "url": "https://mmbiz.qpic.cn/mmbiz_png/example.png",
            "description": "微信公众号图片（可能需要替换为真实链接）"
        }
    ]
    
    print("🖼️ 测试发送图片给 YBA-19990312")
    print("=" * 50)
    
    results = []
    
    for i, img in enumerate(test_images, 1):
        print(f"\n{i}. 测试 {img['name']}")
        print(f"   URL: {img['url']}")
        print(f"   描述: {img['description']}")
        
        request = SendImageRequest(
            wxid="wxid_yba19990312",
            to_wxid="YBA-19990312",
            image_base64=img['url']  # V3 中传入 URL
        )
        
        try:
            print("   📤 发送中...")
            response = adapter.send_image(request)
            
            if response.ok:
                print("   ✅ 发送成功!")
                if response.data:
                    msg_id = response.data.get('newMsgId', 'N/A')
                    print(f"   📋 消息ID: {msg_id}")
                results.append((img['name'], True, "成功"))
            else:
                print(f"   ❌ 发送失败: {response.message}")
                results.append((img['name'], False, response.message))
                
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ 异常: {error_msg}")
            results.append((img['name'], False, error_msg))
    
    # 显示测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for name, success, message in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {name}: {status}")
        if not success:
            print(f"      原因: {message}")
    
    success_count = sum(1 for _, success, _ in results if success)
    print(f"\n总计: {success_count}/{len(results)} 张图片发送成功")
    
    return results


def test_custom_image():
    """测试自定义图片 URL"""
    print("\n🎯 测试自定义图片 URL")
    print("-" * 30)
    
    # 你可以在这里替换为你自己的图片 URL
    custom_url = input("请输入图片 URL (回车跳过): ").strip()
    
    if not custom_url:
        print("⏭️ 跳过自定义图片测试")
        return False
    
    if not custom_url.startswith(('http://', 'https://')):
        print("❌ 无效的 URL 格式")
        return False
    
    adapter = create_adapter("v3",
        v3_base_url="http://api.geweapi.com/gewe/v2/api",
        gewe_token="1f9f8077-d47b-4f64-9227-61eed57c225d",
        gewe_appid="wx_SyxIy_9ZUpnIxmw83Y6Hl"
    )
    
    request = SendImageRequest(
        wxid="wxid_yba19990312",
        to_wxid="YBA-19990312",
        image_base64=custom_url
    )
    
    try:
        print(f"📤 发送自定义图片: {custom_url}")
        response = adapter.send_image(request)
        
        if response.ok:
            print("✅ 自定义图片发送成功!")
            return True
        else:
            print(f"❌ 发送失败: {response.message}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 V3 接口图片发送测试")
    print("=" * 50)
    
    # 测试预设的图片 URL
    results = test_image_urls()
    
    # 测试自定义图片 URL
    custom_success = test_custom_image()
    
    print("\n" + "=" * 50)
    print("💡 使用建议:")
    print("1. 确保图片 URL 可以直接访问（在浏览器中能打开）")
    print("2. 支持的图片格式: JPG, PNG, GIF 等常见格式")
    print("3. 图片大小建议不超过 10MB")
    print("4. 如果使用自己的图片服务器，确保支持 HTTPS")
    print("5. 避免使用需要认证或有防盗链的图片链接")
    
    if any(success for _, success, _ in results) or custom_success:
        print("\n✅ 至少有一张图片发送成功，V3 图片接口工作正常！")
    else:
        print("\n⚠️ 所有图片都发送失败，请检查:")
        print("   - 网络连接是否正常")
        print("   - GeWe Token 和 App ID 是否正确")
        print("   - 图片 URL 是否真实可访问")


if __name__ == "__main__":
    main()
